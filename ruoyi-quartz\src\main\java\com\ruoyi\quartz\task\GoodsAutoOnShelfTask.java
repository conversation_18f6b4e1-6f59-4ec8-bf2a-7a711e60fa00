package com.ruoyi.quartz.task;

import com.ruoyi.custom.domain.Goods;
import com.ruoyi.custom.service.IGoodsService;
import com.ruoyi.framework.manager.AsyncManager;
import com.ruoyi.common.utils.Threads;
import com.ruoyi.system.domain.SysOperLog;
import com.ruoyi.system.service.ISysOperLogService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.Date;
import java.util.List;
import java.util.TimerTask;

/**
 * @Author: y
 * @CreateDate: 2025/1/3 003 9:24
 * @Version: 1.0
 * @Description:
 */
@Component("goodsAutoOnShelfTask")
@Slf4j
public class GoodsAutoOnShelfTask {

    @Autowired
    private IGoodsService goodsService;

    @Autowired
    private ISysOperLogService operLogService;

    public void processAutoOnShelf() {
        try {
            // 1. 处理每周定时上架
            processWeeklyOnShelf();
            // 2. 处理指定时间上架
            processSpecificOnShelf();
        } catch (Exception e) {
            log.error("商品自动上架任务执行异常", e);
        }
    }

    private void processWeeklyOnShelf() {
        // 获取当前时间
        LocalDateTime now = LocalDateTime.now();
        int currentWeekDay = now.getDayOfWeek().getValue();
        String currentTime = now.format(DateTimeFormatter.ofPattern("HH:mm:ss"));
        // 查询符合当前时间的每周上架商品
        List<Goods> weeklyGoods = goodsService.selectWeeklyOnShelfGoods(currentWeekDay, currentTime);

        for (Goods goods : weeklyGoods) {
            updateGoodsStatus(goods);
        }
    }

    private void processSpecificOnShelf() {
        // 获取当前时间
        LocalDateTime now = LocalDateTime.now();

        // 查询符合当前时间的指定时间上架商品
        List<Goods> specificGoods = goodsService.selectSpecificOnShelfGoods(now);

        for (Goods goods : specificGoods) {
            updateGoodsStatus(goods);
        }
    }

    private void updateGoodsStatus(Goods goods) {
        try {
            if ("0".equals(goods.getIsAutoShelved())){
                // 原子性增加库存，防止并发问题
                int i = goodsService.increaseStockForAutoOnShelf(goods.getId(), goods.getOnShelfQuantity());

                SysOperLog operLog = new SysOperLog();
                operLog.setTitle("商品自动上架");
                operLog.setBusinessType(0);
                operLog.setOperName("system");
                operLog.setMethod("goodsAutoOnShelfTask.processAutoOnShelf");
                operLog.setOperParam("商品ID:" + goods.getId() + ",增加库存:" + goods.getOnShelfQuantity());
                operLog.setStatus(i > 0 ? 0 : 1);
                operLog.setOperTime(new Date());
                operLogService.insertOperlog(operLog);

                if (i > 0) {
                    log.info("商品自动上架成功，商品ID：{}，增加库存：{}", goods.getId(), goods.getOnShelfQuantity());
                } else {
                    log.warn("商品自动上架失败，商品ID：{}，可能已经上架过了", goods.getId());
                }
            }
            // 3秒后执行异步更新，重置上架状态
            AsyncManager.me().execute(new TimerTask() {
                @Override
                public void run() {
                    try {
                        // 休眠5秒
                        Threads.sleep(5000);
                        // 重新查询商品信息并更新状态
                        Goods currentGoods = goodsService.selectGoodsById(goods.getId());
                        if (currentGoods != null && "1".equals(currentGoods.getIsAutoShelved())) {
                            currentGoods.setIsAutoShelved("0");  // 更新状态为已完成
                            goodsService.updateGoods(currentGoods);
                            log.info("商品异步更新成功，商品ID：{}", goods.getId());
                        }
                    } catch (Exception e) {
                        log.error("商品异步更新失败，商品ID：" + goods.getId(), e);
                    }
                }
            });
            
        } catch (Exception e) {
            log.error("商品自动上架状态更新失败，商品ID：" + goods.getId(), e);
        }
    }
}