<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" label-width="68px">
      <el-form-item label="兑换商品" prop="goodsId">
        <el-select v-model="queryParams.goodsId" filterable clearable placeholder="请选择推文标题">
          <el-option
            v-for="(item,index) in goodsSelectList"
            :key="index"
            :label="item.title"
            :value="item.id"
          ></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="兑换状态" prop="status">
        <el-select v-model="queryParams.status" filterable clearable placeholder="请选择兑换状态">
          <el-option
            label="未兑换"
            value="0"
          />
          <el-option
            label="已兑换"
            value="1"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="昵称" prop="nickname">
        <el-input
          v-model="queryParams.nickname"
          placeholder="请输入昵称"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="兑换码" prop="redemptionCode">
        <el-input
          v-model="queryParams.redemptionCode"
          placeholder="请输入兑换码"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="兑换时间" prop="exchangeTime">
        <el-date-picker clearable
          v-model="queryParams.exchangeTime"
          type="date"
          value-format="yyyy-MM-dd"
          placeholder="请选择兑换时间">
        </el-date-picker>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
<!--      <el-col :span="1.5">-->
<!--        <el-button-->
<!--          type="primary"-->
<!--          plain-->
<!--          icon="el-icon-plus"-->
<!--          size="mini"-->
<!--          @click="handleAdd"-->
<!--          v-hasPermi="['custom:order:add']"-->
<!--        >新增</el-button>-->
<!--      </el-col>-->
<!--      <el-col :span="1.5">-->
<!--        <el-button-->
<!--          type="success"-->
<!--          plain-->
<!--          icon="el-icon-edit"-->
<!--          size="mini"-->
<!--          :disabled="single"-->
<!--          @click="handleUpdate"-->
<!--          v-hasPermi="['custom:order:edit']"-->
<!--        >修改</el-button>-->
<!--      </el-col>-->
<!--      <el-col :span="1.5">-->
<!--        <el-button-->
<!--          type="danger"-->
<!--          plain-->
<!--          icon="el-icon-delete"-->
<!--          size="mini"-->
<!--          :disabled="multiple"-->
<!--          @click="handleDelete"-->
<!--          v-hasPermi="['custom:order:remove']"-->
<!--        >删除</el-button>-->
<!--      </el-col>-->
      <el-col :span="1.5">
        <el-button
          type="warning"
          plain
          icon="el-icon-download"
          size="mini"
          @click="handleExport"
          v-hasPermi="['custom:order:export']"
        >导出</el-button>
      </el-col>
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table border v-loading="loading" :data="orderList" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column label="ID" align="center" prop="id" width="80"/>
      <el-table-column label="昵称" align="center" prop="nickname" width="150" :show-overflow-tooltip="true"/>
      <el-table-column label="兑换用户openid" align="center" prop="openid" width="260" :show-overflow-tooltip="true"/>
      <el-table-column label="商品名称" header-align="center" align="left" prop="goodsName" width="300" :show-overflow-tooltip="true"/>
      <el-table-column label="兑换时间" align="center" prop="createTime" width="200" :show-overflow-tooltip="true">
        <template slot-scope="scope">
          <span>{{scope.row.createTime}}</span>
        </template>
      </el-table-column>
      <el-table-column label="兑换积分" align="center" prop="integralNumber" width="150"/>
      <el-table-column label="兑换码" align="center" prop="redemptionCode" width="230" :show-overflow-tooltip="true">
        <template slot-scope="scope">
          <el-tag type="info">{{scope.row.redemptionCode}}</el-tag>
        </template>
      </el-table-column>
      <el-table-column label="兑换状态" align="center" prop="status">
        <template slot-scope="scope">
          <el-tag v-if="scope.row.status === '0'" size="small">未兑换</el-tag>
          <el-tag v-else-if="scope.row.status === '1'" type="success" size="small">已兑换</el-tag>
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width" fixed="right" width="160">
        <template slot-scope="scope">
<!--          <el-button-->
<!--            size="mini"-->
<!--            type="text"-->
<!--            icon="el-icon-edit"-->
<!--            @click="handleUpdate(scope.row)"-->
<!--            v-hasPermi="['custom:order:edit']"-->
<!--          >修改</el-button>-->
<!--          <el-button-->
<!--            size="mini"-->
<!--            type="text"-->
<!--            icon="el-icon-delete"-->
<!--            @click="handleDelete(scope.row)"-->
<!--            v-hasPermi="['custom:order:remove']"-->
<!--          >删除</el-button>-->
          <el-button
            v-if="scope.row.status === '0'"
            size="mini"
            type="text"
            icon="el-icon-s-claim"
            @click="handleHeXiao(scope.row)"
            v-hasPermi="['custom:order:edit']"
          >核销</el-button>
          <el-tooltip v-if="scope.row.status === '1'" class="item" effect="dark" :content="'核销时间:' + scope.row.exchangeTime" placement="top">
            <el-button
                size="mini"
                type="text"
                icon="el-icon-success"
                v-hasPermi="['custom:order:edit']"
            >已核销</el-button>
          </el-tooltip>
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 添加或修改订单对话框 -->
<!--    <el-dialog :title="title" :visible.sync="open" width="40%" append-to-body>-->
<!--      <el-form ref="form" :model="form" :rules="rules" label-width="80px">-->
<!--        <el-form-item label="兑换用户openid" prop="openid">-->
<!--          <el-input v-model="form.openid" placeholder="请输入兑换用户openid" />-->
<!--        </el-form-item>-->
<!--        <el-form-item label="商品id" prop="goodsId">-->
<!--          <el-input v-model="form.goodsId" placeholder="请输入商品id" />-->
<!--        </el-form-item>-->
<!--        <el-form-item label="兑换时间" prop="exchangeTime">-->
<!--          <el-date-picker clearable-->
<!--            v-model="form.exchangeTime"-->
<!--            type="date"-->
<!--            value-format="yyyy-MM-dd"-->
<!--            placeholder="请选择兑换时间">-->
<!--          </el-date-picker>-->
<!--        </el-form-item>-->
<!--        <el-form-item label="兑换积分" prop="integralNumber">-->
<!--          <el-input v-model="form.integralNumber" placeholder="请输入兑换积分" />-->
<!--        </el-form-item>-->
<!--        <el-form-item label="兑换码" prop="redemptionCode">-->
<!--          <el-input v-model="form.redemptionCode" placeholder="请输入兑换码" />-->
<!--        </el-form-item>-->
<!--      </el-form>-->
<!--      <div slot="footer" class="dialog-footer">-->
<!--        <el-button type="primary" @click="submitForm">确 定</el-button>-->
<!--        <el-button @click="cancel">取 消</el-button>-->
<!--      </div>-->
<!--    </el-dialog>-->
  </div>
</template>

<script>
import { listOrder, getOrder, delOrder, addOrder, updateOrder,updateOrderStatus} from "@/api/custom/order";
import {selectListGoods} from "../../../api/custom/goods";

export default {
  name: "Order",
  data() {
    return {
      goodsSelectList: [],
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 订单表格数据
      orderList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        openid: null,
        goodsId: null,
        exchangeTime: null,
        status: null,
        nickname: null,
        integralNumber: null,
        redemptionCode: null,
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {
        openid: [
          { required: true, message: "兑换用户openid不能为空", trigger: "blur" }
        ],
        goodsId: [
          { required: true, message: "商品id不能为空", trigger: "blur" }
        ],
        exchangeTime: [
          { required: true, message: "兑换时间不能为空", trigger: "blur" }
        ],
        status: [
          { required: true, message: "兑换状态0未兑换，1已兑换不能为空", trigger: "change" }
        ],
        integralNumber: [
          { required: true, message: "兑换积分不能为空", trigger: "blur" }
        ],
        redemptionCode: [
          { required: true, message: "兑换码不能为空", trigger: "blur" }
        ],
        createTime: [
          { required: true, message: "不能为空", trigger: "blur" }
        ],
        updateTime: [
          { required: true, message: "不能为空", trigger: "blur" }
        ]
      }
    };
  },
  created() {
    this.getList();
    this.getGoodsSelect();
  },
  methods: {
    getGoodsSelect() {
      selectListGoods().then(response => {
        this.goodsSelectList = response.rows;
      });
    },
    /** 查询订单列表 */
    getList() {
      this.loading = true;
      listOrder(this.queryParams).then(response => {
        this.orderList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        id: null,
        openid: null,
        goodsId: null,
        exchangeTime: null,
        status: null,
        integralNumber: null,
        redemptionCode: null,
        createTime: null,
        updateTime: null
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.id)
      this.single = selection.length!==1
      this.multiple = !selection.length
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.open = true;
      this.title = "添加订单";
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      const id = row.id || this.ids
      getOrder(id).then(response => {
        this.form = response.data;
        this.open = true;
        this.title = "修改订单";
      });
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate(valid => {
        if (valid) {
          if (this.form.id != null) {
            updateOrder(this.form).then(response => {
              this.$modal.msgSuccess("修改成功");
              this.open = false;
              this.getList();
            });
          } else {
            addOrder(this.form).then(response => {
              this.$modal.msgSuccess("新增成功");
              this.open = false;
              this.getList();
            });
          }
        }
      });
    },
    handleHeXiao(row){
      this.$modal.confirm('是否确认核销兑换码为【' + row.redemptionCode + '】的数据项？').then(function() {
        return updateOrderStatus(row.id).then(response => {
        });
      }).then(() => {
        this.getList();
        this.$modal.msgSuccess("核销成功");
      }).catch(() => {});
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const ids = row.id || this.ids;``
      this.$modal.confirm('是否确认删除订单编号为"' + ids + '"的数据项？').then(function() {
        return delOrder(ids);
      }).then(() => {
        this.getList();
        this.$modal.msgSuccess("删除成功");
      }).catch(() => {});
    },
    /** 导出按钮操作 */
    handleExport() {
      if (this.ids.length === 0) {
        this.$modal.msgWarning("请勾选需要导出的数据");
        return;
      }
      this.$modal.confirm('是否确认导出所选订单数据？').then(() => {
        this.download('custom/order/export', {
          ids: this.ids.join(',')
        }, `兑换记录_${new Date().getTime()}.xlsx`)
      }).catch(() => {});
    }
  }
};
</script>
