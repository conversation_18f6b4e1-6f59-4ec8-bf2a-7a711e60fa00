package com.ruoyi.custom.service;

import java.util.List;

import com.alibaba.fastjson2.JSONObject;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.custom.domain.IntegralRecord;

/**
 * 积分记录Service接口
 * 
 * <AUTHOR>
 * @date 2024-05-15
 */
public interface IIntegralRecordService 
{
    /**
     * 查询积分记录
     * 
     * @param id 积分记录主键
     * @return 积分记录
     */
    public IntegralRecord selectIntegralRecordById(Long id);

    /**
     * 查询积分记录列表
     * 
     * @param integralRecord 积分记录
     * @return 积分记录集合
     */
    public List<IntegralRecord> selectIntegralRecordList(IntegralRecord integralRecord);
    public List<IntegralRecord> selectIntegralRecordList2(IntegralRecord integralRecord);

    /**
     * 新增积分记录
     * 
     * @param integralRecord 积分记录
     * @return 结果
     */
    public int insertIntegralRecord(IntegralRecord integralRecord);

    /**
     * 修改积分记录
     * 
     * @param integralRecord 积分记录
     * @return 结果
     */
    public int updateIntegralRecord(IntegralRecord integralRecord);

    /**
     * 批量删除积分记录
     * 
     * @param ids 需要删除的积分记录主键集合
     * @return 结果
     */
    public int deleteIntegralRecordByIds(Long[] ids);

    /**
     * 删除积分记录信息
     * 
     * @param id 积分记录主键
     * @return 结果
     */
    public int deleteIntegralRecordById(Long id);

    AjaxResult integralList(String token);


    AjaxResult oneWeekPoints(String dateRange);

    List<JSONObject> numberOfViewers(List<String> dateRangeList);

    List<JSONObject> numberOfPointsObtained(List<String> dateRangeList);

}
