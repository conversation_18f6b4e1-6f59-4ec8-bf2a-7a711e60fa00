# 商品兑换超卖问题修复说明

## 问题描述

在商品兑换系统中，当商品库存为1时，多个用户同时兑换会出现超卖现象：
- 商品库存为1，但是有2个或更多用户成功兑换
- 库存没有变成负数，而是停留在0
- 订单数量超过了实际库存数量

## 问题原因分析

### 1. 并发控制不足
原有的分布式锁粒度过粗，只针对商品ID加锁，在高并发情况下仍可能出现竞态条件。

### 2. 数据库操作非原子性
原有的库存更新方式：
```java
// 先查询库存
Goods goods = goodsMapper.selectGoodsForUpdate(goodsId);
// 基于查询结果计算新库存
goods.setStock(goods.getStock() - 1);
// 更新库存
goodsMapper.updateGoods(goods);
```

这种方式在并发情况下存在时间窗口，可能导致多个线程都读取到相同的库存值。

### 3. 锁的时机和范围问题
虽然使用了`FOR UPDATE`行锁，但锁的释放时机和业务逻辑执行之间存在间隙。

## 修复方案

### 1. 优化分布式锁策略
```java
// 修改前：只针对商品加锁（粒度过粗）
lockKey = "exchange_lock:" + goodsId;

// 修改后：针对商品加锁（确保同一商品同时只能有一个兑换操作）
lockKey = "exchange_goods_lock:" + goodsId;
```

**优势：**
- 确保同一商品在同一时间只能有一个兑换操作进行
- 防止不同用户同时兑换同一商品导致超卖
- 锁的超时时间调整为30秒，提高响应速度
- 锁的值设置为用户openid，便于问题追踪

### 2. 实现原子性库存更新
新增原子性减库存SQL：
```sql
UPDATE tbl_goods 
SET stock = stock - 1, 
    sales_volume = sales_volume + 1,
    update_time = NOW()
WHERE id = #{goodsId} 
  AND stock > 0
```

**关键特性：**
- 使用`stock > 0`条件确保不会超卖
- 原子性操作，避免并发问题
- 返回影响行数，0表示库存不足

### 4. 修改业务逻辑流程

#### 兑换逻辑修改
```java
// 修改前：先查询再更新
Goods goods = goodsMapper.selectGoodsForUpdate(goodsId);
goods.setStock(goods.getStock() - 1);
goodsMapper.updateGoods(goods);

// 修改后：原子性更新
int result = goodsMapper.decreaseStockAndIncreaseSales(goodsId);
if (result <= 0) {
    return AjaxResult.error("商品库存不足！");
}
```

## 修复内容详细说明

### 1. 数据库层面修改

#### GoodsMapper.xml
新增原子性减库存方法：
```xml
<update id="decreaseStockAndIncreaseSales" parameterType="map">
    UPDATE tbl_goods
    SET stock = stock - 1,
        sales_volume = sales_volume + 1,
        update_time = NOW()
    WHERE id = #{goodsId}
      AND stock > 0
</update>
```

#### GoodsMapper.java
新增接口方法：
```java
/**
 * 原子性减库存并增加销量，防止超卖
 * @param goodsId 商品ID
 * @return 影响的行数，如果为0说明库存不足
 */
int decreaseStockAndIncreaseSales(@Param("goodsId") Long goodsId);


### 2. 业务逻辑层面修改

#### OrderServiceImpl.java
主要修改点：

1. **优化分布式锁**：
   - 使用商品级别的锁，确保同一商品同时只能有一个兑换操作
   - 锁超时时间调整为30秒，提高响应速度
   - 锁的值设置为用户openid，便于问题追踪
   - 错误提示更加友好

2. **原子性库存更新**：
   - 移除基于查询结果的库存计算
   - 使用原子性SQL操作
   - 根据影响行数判断是否成功

3. **简化商品查询**：
   - 移除不必要的`FOR UPDATE`锁
   - 只用于状态检查和积分验证

4. **优化日志记录**：
   - 添加关键节点的详细日志记录
   - 包含线程信息、执行耗时、影响行数等关键信息
   - 添加超卖检测日志，便于问题追踪
   - 区分不同级别的日志（INFO、WARN、ERROR）

5. **添加库存验证机制**：
   - 在库存扣减后立即验证库存状态
   - 检测是否出现负库存
   - 验证库存变化是否符合预期
   - 提供详细的超卖警告日志
