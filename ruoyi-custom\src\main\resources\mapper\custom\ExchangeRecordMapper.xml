<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.custom.mapper.ExchangeRecordMapper">
    
    <resultMap type="ExchangeRecord" id="ExchangeRecordResult">
        <result property="id"    column="id"    />
        <result property="uId"    column="u_id"    />
        <result property="gId"    column="g_id"    />
        <result property="exchangeDate"    column="exchange_date"    />
        <result property="status"    column="status"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateTime"    column="update_time"    />
    </resultMap>

    <sql id="selectExchangeRecordVo">
        select id, u_id, g_id, exchange_date, status, create_time, update_time from tbl_exchange_record
    </sql>

    <select id="selectExchangeRecordList" parameterType="ExchangeRecord" resultMap="ExchangeRecordResult">
        <include refid="selectExchangeRecordVo"/>
        <where>  
            <if test="uId != null "> and u_id = #{uId}</if>
            <if test="gId != null "> and g_id = #{gId}</if>
            <if test="exchangeDate != null "> and exchange_date = #{exchangeDate}</if>
            <if test="status != null "> and status = #{status}</if>
        </where>
    </select>
    
    <select id="selectExchangeRecordById" parameterType="Long" resultMap="ExchangeRecordResult">
        <include refid="selectExchangeRecordVo"/>
        where id = #{id}
    </select>
    <select id="selectExchangeRecordByUidAndGoodsId" resultType="java.lang.Integer">
        SELECT count(*) AS num
        FROM tbl_exchange_record
        WHERE u_id = #{openid}
          AND g_id = #{goodsId}
          AND DATE(exchange_date) = CURDATE();
    </select>

    <insert id="insertExchangeRecord" parameterType="ExchangeRecord">
        insert into tbl_exchange_record
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">id,</if>
            <if test="uId != null">u_id,</if>
            <if test="gId != null">g_id,</if>
            <if test="exchangeDate != null">exchange_date,</if>
            <if test="status != null">status,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateTime != null">update_time,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id},</if>
            <if test="uId != null">#{uId},</if>
            <if test="gId != null">#{gId},</if>
            <if test="exchangeDate != null">#{exchangeDate},</if>
            <if test="status != null">#{status},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateTime != null">#{updateTime},</if>
         </trim>
    </insert>

    <update id="updateExchangeRecord" parameterType="ExchangeRecord">
        update tbl_exchange_record
        <trim prefix="SET" suffixOverrides=",">
            <if test="uId != null">u_id = #{uId},</if>
            <if test="gId != null">g_id = #{gId},</if>
            <if test="exchangeDate != null">exchange_date = #{exchangeDate},</if>
            <if test="status != null">status = #{status},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteExchangeRecordById" parameterType="Long">
        delete from tbl_exchange_record where id = #{id}
    </delete>

    <delete id="deleteExchangeRecordByIds" parameterType="String">
        delete from tbl_exchange_record where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>