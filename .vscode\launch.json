{"configurations": [{"type": "java", "name": "TestController", "request": "launch", "mainClass": "com.ruoyi.custom.controller.TestController", "projectName": "ruoyi-custom"}, {"type": "java", "name": "Spring Boot-RuoYiApplication<ruoyi-admin>", "request": "launch", "cwd": "${workspaceFolder}", "mainClass": "com.ruoyi.RuoYiApplication", "projectName": "ruoyi-admin", "args": "", "envFile": "${workspaceFolder}/.env", "vmArgs": " -Dcom.sun.management.jmxremote -Dcom.sun.management.jmxremote.port=64007 -Dcom.sun.management.jmxremote.authenticate=false -Dcom.sun.management.jmxremote.ssl=false -Dspring.jmx.enabled=true -Djava.rmi.server.hostname=localhost -Dspring.application.admin.enabled=true -Dspring.boot.project.name=ruoyi-admin"}]}