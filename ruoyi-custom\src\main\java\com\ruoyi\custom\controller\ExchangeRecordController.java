package com.ruoyi.custom.controller;

import java.util.List;
import javax.servlet.http.HttpServletResponse;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.custom.domain.ExchangeRecord;
import com.ruoyi.custom.service.IExchangeRecordService;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.common.core.page.TableDataInfo;

/**
 * 兑换记录Controller
 * 
 * <AUTHOR>
 * @date 2025-01-02
 */
@RestController
@RequestMapping("/custom/exchangeRecord")
public class ExchangeRecordController extends BaseController
{
    @Autowired
    private IExchangeRecordService exchangeRecordService;

    /**
     * 查询兑换记录列表
     */
    @PreAuthorize("@ss.hasPermi('custom:exchangeRecord:list')")
    @GetMapping("/list")
    public TableDataInfo list(ExchangeRecord exchangeRecord)
    {
        startPage();
        List<ExchangeRecord> list = exchangeRecordService.selectExchangeRecordList(exchangeRecord);
        return getDataTable(list);
    }

    /**
     * 导出兑换记录列表
     */
    @PreAuthorize("@ss.hasPermi('custom:exchangeRecord:export')")
    @Log(title = "兑换记录", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, ExchangeRecord exchangeRecord)
    {
        List<ExchangeRecord> list = exchangeRecordService.selectExchangeRecordList(exchangeRecord);
        ExcelUtil<ExchangeRecord> util = new ExcelUtil<ExchangeRecord>(ExchangeRecord.class);
        util.exportExcel(response, list, "兑换记录数据");
    }

    /**
     * 获取兑换记录详细信息
     */
    @PreAuthorize("@ss.hasPermi('custom:exchangeRecord:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return success(exchangeRecordService.selectExchangeRecordById(id));
    }

    /**
     * 新增兑换记录
     */
    @PreAuthorize("@ss.hasPermi('custom:exchangeRecord:add')")
    @Log(title = "兑换记录", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody ExchangeRecord exchangeRecord)
    {
        return toAjax(exchangeRecordService.insertExchangeRecord(exchangeRecord));
    }

    /**
     * 修改兑换记录
     */
    @PreAuthorize("@ss.hasPermi('custom:exchangeRecord:edit')")
    @Log(title = "兑换记录", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody ExchangeRecord exchangeRecord)
    {
        return toAjax(exchangeRecordService.updateExchangeRecord(exchangeRecord));
    }

    /**
     * 删除兑换记录
     */
    @PreAuthorize("@ss.hasPermi('custom:exchangeRecord:remove')")
    @Log(title = "兑换记录", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        return toAjax(exchangeRecordService.deleteExchangeRecordByIds(ids));
    }
}
