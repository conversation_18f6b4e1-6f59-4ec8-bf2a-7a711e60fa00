<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.custom.mapper.IntegralRecordMapper">
    
    <resultMap type="IntegralRecord" id="IntegralRecordResult">
        <result property="id"    column="id"    />
        <result property="openid"    column="openid"    />
        <result property="articleId"    column="article_id"    />
        <result property="integralNumber"    column="integral_number"    />
        <result property="type"    column="type"    />
        <result property="remark"    column="remark"    />
        <result property="operationTime"    column="operation_time"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateTime"    column="update_time"    />
    </resultMap>

    <resultMap type="IntegralRecord" id="IntegralRecordResult2">
        <result property="id"    column="id"    />
        <result property="openid"    column="openid"    />
        <result property="articleId"    column="article_id"    />
        <result property="integralNumber"    column="integral_number"    />
        <result property="type"    column="type"    />
        <result property="remark"    column="remark"    />
        <result property="operationTime"    column="operation_time"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateTime"    column="update_time"    />
        <result property="articleTitle"    column="article_title"    />
        <result property="nickname"    column="nickname"    />
    </resultMap>

    <sql id="selectIntegralRecordVo">
        select id, openid, article_id, integral_number, type, remark, operation_time, create_time, update_time from tbl_integral_record
    </sql>

    <select id="selectIntegralRecordList" parameterType="IntegralRecord" resultMap="IntegralRecordResult">
        <include refid="selectIntegralRecordVo"/>
        <where>  
            <if test="openidList != null  and openidList != ''">
            and openid in
            <foreach collection="openidList" item="item" index="index"
                     separator="," open="(" close=")">
                #{item}
            </foreach>
             </if>
            <if test="articleId != null "> and article_id = #{articleId}</if>
            <if test="openid != null "> and openid = #{openid}</if>
            <if test="integralNumber != null "> and integral_number = #{integralNumber}</if>
            <if test="type != null "> and type = #{type}</if>
            <if test="operationTime != null "> and DATE( operation_time ) = #{operationTime}</if>
        </where>
        order by id desc
    </select>
    <select id="selectIntegralRecordList2" parameterType="IntegralRecord" resultMap="IntegralRecordResult2">
        SELECT 
            ir.id,
            ir.openid,
            ir.article_id,
            ir.integral_number,
            ir.type,
            ir.remark,
            ir.operation_time,
            ir.create_time,
            ir.update_time,
            a.title as article_title,
            u.nickname
        FROM tbl_integral_record ir
        LEFT JOIN tbl_article a ON ir.article_id = a.id
        LEFT JOIN tbl_mp_user u ON ir.openid = u.openid
        <where>
            <if test="openidList != null  and openidList != ''">
            and ir.openid in
            <foreach collection="openidList" item="item" index="index"
                     separator="," open="(" close=")">
                #{item}
            </foreach>
             </if>
            <if test="articleId != null "> and ir.article_id = #{articleId}</if>
            <if test="openid != null "> and ir.openid = #{openid}</if>
            <if test="integralNumber != null "> and ir.integral_number = #{integralNumber}</if>
            <if test="type != null "> and ir.type = #{type}</if>
            <if test="operationTime != null "> and DATE( ir.operation_time ) = #{operationTime}</if>
        </where>
        order by ir.id desc
    </select>
    <select id="selectIntegralRecordListByOpenIdAndArticleId"
            resultMap="IntegralRecordResult">
        <include refid="selectIntegralRecordVo"/>
        where openid = #{openid} and article_id = #{articleId}
    </select>
    <select id="selectIntegralRecordById" parameterType="Long" resultMap="IntegralRecordResult">
        <include refid="selectIntegralRecordVo"/>
        where id = #{id}
    </select>

    <select id="selectIntegralRecordListByOpenid" parameterType="String" resultMap="IntegralRecordResult">
        select  integral_number,article_id, type, remark, operation_time, create_time, update_time from tbl_integral_record
        where openid = #{openid}
        order by id desc LIMIT 30
    </select>
    <select id="oneWeekPoints" parameterType="List" resultType="com.alibaba.fastjson2.JSONObject">
        SELECT
        SUM( integral_number ) AS number,
        DATE( create_time ) datetime
        FROM
        `tbl_integral_record`
        WHERE
        type = 0 AND
        DATE( create_time ) IN
        <foreach item="dateTime" collection="dateTimeList" open="(" separator="," close=")">
            #{dateTime}
        </foreach>
        GROUP BY
        datetime
        ORDER BY
        datetime ASC
    </select>
    <select id="numberOfViewers" parameterType="List" resultType="com.alibaba.fastjson2.JSONObject">
        SELECT
        COUNT( DISTINCT openid ) AS number,
        DATE( operation_time ) AS ot
        FROM
        `tbl_integral_record`
        WHERE
        type = 0
        AND DATE( operation_time ) IN
            <foreach item="item" collection="list" open="(" separator="," close=")">
                #{item}
            </foreach>
        GROUP BY
        ot
        ORDER BY
        ot ASC
    </select>
    <select id="selectDailyMaximumPointsByOpenidAndCurDate" resultType="java.lang.Integer">
        SELECT COALESCE
               ( sum( integral_number ), 0 ) AS number
        FROM
            `tbl_integral_record`
        WHERE
            openid = #{openid}
          AND DATE( operation_time ) = CURDATE()
    </select>
    <select id="numberOfPointsObtained" parameterType="List" resultType="com.alibaba.fastjson2.JSONObject">
        SELECT
            SUM( integral_number ) AS number,
            DATE( operation_time ) AS ot
        FROM
            `tbl_integral_record`
        WHERE
            type = 0
          AND DATE( operation_time ) IN
            <foreach item="item" collection="list" open="(" separator="," close=")">
                #{item}
            </foreach>
        GROUP BY
            ot
        ORDER BY
        ot ASC
    </select>
    <select id="selectReadNumberByArticleId" resultType="java.lang.Integer">
        SELECT COUNT(*) AS number FROM tbl_integral_record WHERE article_id = #{articleId}
    </select>

    <insert id="insertIntegralRecord" parameterType="IntegralRecord" useGeneratedKeys="true" keyProperty="id">
        insert into tbl_integral_record
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="openid != null and openid != ''">openid,</if>
            <if test="articleId != null">article_id,</if>
            <if test="integralNumber != null">integral_number,</if>
            <if test="type != null">type,</if>
            <if test="remark != null and remark != ''">remark,</if>
            <if test="operationTime != null">operation_time,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateTime != null">update_time,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="openid != null and openid != ''">#{openid},</if>
            <if test="articleId != null">#{articleId},</if>
            <if test="integralNumber != null">#{integralNumber},</if>
            <if test="type != null">#{type},</if>
            <if test="remark != null and remark != ''">#{remark},</if>
            <if test="operationTime != null">#{operationTime},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateTime != null">#{updateTime},</if>
         </trim>
    </insert>

    <update id="updateIntegralRecord" parameterType="IntegralRecord">
        update tbl_integral_record
        <trim prefix="SET" suffixOverrides=",">
            <if test="openid != null and openid != ''">openid = #{openid},</if>
            <if test="articleId != null">article_id = #{articleId},</if>
            <if test="integralNumber != null">integral_number = #{integralNumber},</if>
            <if test="type != null">type = #{type},</if>
            <if test="remark != null and remark != ''">remark = #{remark},</if>
            <if test="operationTime != null">operation_time = #{operationTime},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
        </trim>
        where id = #{id}
    </update>
    <delete id="deleteIntegralRecordById" parameterType="Long">
        delete from tbl_integral_record where id = #{id}
    </delete>

    <delete id="deleteIntegralRecordByIds" parameterType="String">
        delete from tbl_integral_record where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>