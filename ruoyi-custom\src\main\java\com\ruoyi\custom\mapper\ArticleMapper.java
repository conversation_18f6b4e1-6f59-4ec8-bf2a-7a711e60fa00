package com.ruoyi.custom.mapper;

import java.util.List;
import com.ruoyi.custom.domain.Article;
import org.apache.ibatis.annotations.Param;

/**
 * 文章Mapper接口
 * 
 * <AUTHOR>
 * @date 2024-05-14
 */
public interface ArticleMapper 
{
    /**
     * 查询文章
     * 
     * @param id 文章主键
     * @return 文章
     */
    public Article selectArticleById(Long id);

    /**
     * 查询文章列表
     * 
     * @param article 文章
     * @return 文章集合
     */
    public List<Article> selectArticleList(Article article);

    /**
     * 新增文章
     * 
     * @param article 文章
     * @return 结果
     */
    public int insertArticle(Article article);

    /**
     * 修改文章
     * 
     * @param article 文章
     * @return 结果
     */
    public int updateArticle(Article article);

    /**
     * 删除文章
     * 
     * @param id 文章主键
     * @return 结果
     */
    public int deleteArticleById(Long id);

    /**
     * 批量删除文章
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteArticleByIds(Long[] ids);

    Article selectArticleByUrlLike(String articleId);

    int selectArticleAllCount();

    List<Article> selectArticleListByDotLike(@Param("openid") String openid);
}
