package com.ruoyi.custom.mapper;

import java.util.List;

import com.alibaba.fastjson2.JSONObject;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.custom.domain.IntegralRecord;
import org.apache.ibatis.annotations.Param;

/**
 * 积分记录Mapper接口
 * 
 * <AUTHOR>
 * @date 2024-05-15
 */
public interface IntegralRecordMapper 
{
    /**
     * 查询积分记录
     * 
     * @param id 积分记录主键
     * @return 积分记录
     */
    public IntegralRecord selectIntegralRecordById(Long id);

    /**
     * 查询积分记录列表
     * 
     * @param integralRecord 积分记录
     * @return 积分记录集合
     */
    public List<IntegralRecord> selectIntegralRecordList(IntegralRecord integralRecord);
    public List<IntegralRecord> selectIntegralRecordList2(IntegralRecord integralRecord);

    /**
     * 新增积分记录
     * 
     * @param integralRecord 积分记录
     * @return 结果
     */
    public int insertIntegralRecord(IntegralRecord integralRecord);

    /**
     * 修改积分记录
     * 
     * @param integralRecord 积分记录
     * @return 结果
     */
    public int updateIntegralRecord(IntegralRecord integralRecord);

    /**
     * 删除积分记录
     * 
     * @param id 积分记录主键
     * @return 结果
     */
    public int deleteIntegralRecordById(Long id);

    /**
     * 批量删除积分记录
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteIntegralRecordByIds(Long[] ids);

    List<IntegralRecord> selectIntegralRecordListByOpenid(String openid);

    List<JSONObject> oneWeekPoints(@Param("dateTimeList") List<String> dateTimeList);

    List<IntegralRecord> selectIntegralRecordListByOpenIdAndArticleId(@Param("openid")String openid, @Param("articleId")Long articleId);

    int selectDailyMaximumPointsByOpenidAndCurDate(@Param("openid") String openid);

    List<JSONObject> numberOfViewers(@Param("list") List<String> dateRangeList);

    List<JSONObject> numberOfPointsObtained(@Param("list") List<String> dateRangeList);

    int selectReadNumberByArticleId(@Param("articleId") Long articleId);
}
