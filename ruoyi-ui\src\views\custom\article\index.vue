<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" label-width="68px">
      <el-form-item label="标题" prop="title">
        <el-input
          v-model="queryParams.title"
          placeholder="请输入标题"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
<!--      <el-form-item label="链接" prop="url">-->
<!--        <el-input-->
<!--          v-model="queryParams.url"-->
<!--          placeholder="请输入链接"-->
<!--          clearable-->
<!--          @keyup.enter.native="handleQuery"-->
<!--        />-->
<!--      </el-form-item>-->
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="primary"
          plain
          icon="el-icon-plus"
          size="mini"
          @click="handleAdd"
          v-hasPermi="['custom:article:add']"
        >新增</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="success"
          plain
          icon="el-icon-edit"
          size="mini"
          :disabled="single"
          @click="handleUpdate"
          v-hasPermi="['custom:article:edit']"
        >修改</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="danger"
          plain
          icon="el-icon-delete"
          size="mini"
          :disabled="multiple"
          @click="handleDelete"
          v-hasPermi="['custom:article:remove']"
        >删除</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="warning"
          plain
          icon="el-icon-download"
          size="mini"
          @click="handleExport"
          v-hasPermi="['custom:article:export']"
        >导出</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-dropdown @command="handleColumnCommand" trigger="click">
          <el-button type="info" plain size="mini">
            列设置<i class="el-icon-arrow-down el-icon--right"></i>
          </el-button>
          <el-dropdown-menu slot="dropdown">
            <el-dropdown-item v-for="item in columns"
              :key="item.prop"
              :command="item.prop"
              >
              <el-checkbox v-model="item.visible" @click.native.stop>{{item.label}}</el-checkbox>
            </el-dropdown-item>
            <el-dropdown-item divided command="reset">
              <i class="el-icon-refresh"></i> 重置列设置
            </el-dropdown-item>
          </el-dropdown-menu>
        </el-dropdown>
      </el-col>
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table border v-loading="loading" :data="articleList" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column label="ID" align="center" prop="id" min-width="50" v-if="columns[0].visible"/>
      <el-table-column label="推文标题" header-align="center" align="left" prop="title" width="400" :show-overflow-tooltip="true" v-if="columns[1].visible"/>
      <el-table-column label="原文链接" align="center" prop="url" min-width="550" :show-overflow-tooltip="true" v-if="columns[2].visible">
        <template slot-scope="scope">
          <el-tag type="info" style="font-family: monospace; color: #606266">{{scope.row.url}}</el-tag>
          &nbsp; <el-tooltip class="item" effect="dark" content="点击复制原文链接" :open-delay="500" placement="top-start">
              <el-button v-clipboard:copy="scope.row.url"
                     v-clipboard:success="onCopySuccess"
                     v-clipboard:error="onCopyError"
                     size="mini" icon="el-icon-document-copy"></el-button>
            </el-tooltip>
        </template>
      </el-table-column>
      <el-table-column label="阅读数量" align="center" prop="readNumber" min-width="80" v-if="columns[3].visible"/>
      <el-table-column label="备注" align="center" prop="remark" min-width="120" :show-overflow-tooltip="true" v-if="columns[4].visible"/>
      <el-table-column label="状态" align="center" prop="status" min-width="80" :show-overflow-tooltip="true" v-if="columns[5].visible">
        <template slot-scope="scope">
          <el-tag v-if="scope.row.status == 0" type="success">正常</el-tag>
          <el-tag v-else-if="scope.row.status == 1" type="danger">停用</el-tag>
        </template>
      </el-table-column>
      <el-table-column label="创建时间" align="center" prop="createTime" width="180" v-if="columns[6].visible">
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.createTime) }}</span>
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width" min-width="120" fixed="right" v-if="columns[7].visible">
        <template slot-scope="scope">
          <el-button
            size="mini"
            type="text"
            icon="el-icon-edit"
            @click="handleUpdate(scope.row)"
            v-hasPermi="['custom:article:edit']"
          >修改</el-button>
          <el-button
            size="mini"
            type="text"
            icon="el-icon-delete"
            @click="handleDelete(scope.row)"
            v-hasPermi="['custom:article:remove']"
          >删除</el-button>
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 添加或修改推文对话框 -->
    <el-dialog :title="title" :visible.sync="open" width="40%" append-to-body>
      <el-form ref="form" :model="form" :rules="rules" label-width="80px">
        <el-form-item label="标题" prop="title">
          <el-input v-model="form.title" placeholder="请输入标题" />
        </el-form-item>
        <el-form-item v-if="this.form.id" label="链接" prop="url">
          <el-input v-model="form.url" placeholder="请输入链接" />
        </el-form-item>
        <el-form-item label="状态" prop="status">
            <el-radio-group v-model="form.status">
              <el-radio
                v-for="dict in dict.type.sys_normal_disable"
                :key="dict.value"
                :label="dict.value"
              >{{dict.label}}</el-radio>
            </el-radio-group>
        </el-form-item>
        <el-form-item label="备注" prop="remark">
          <el-input type="textarea" :rows="3" v-model="form.remark" placeholder="请输入备注" />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { listArticle, getArticle, delArticle, addArticle, updateArticle } from "@/api/custom/article";

export default {
  name: "Article",
  dicts: ['sys_normal_disable'],
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 推文表格数据
      articleList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        title: null,
        url: null,
        status: null,
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {
        title: [
          { required: true, message: "标题不能为空", trigger: "blur" }
        ],
        url: [
          { required: true, message: "链接不能为空", trigger: "blur" }
        ],
        status: [
          { required: true, message: "$comment不能为空", trigger: "change" }
        ],
      },
      // 列显示设置
      columns: [
        { label: 'ID', prop: 'id', visible: true },
        { label: '推文标题', prop: 'title', visible: true },
        { label: '原文链接', prop: 'url', visible: true },
        { label: '阅读数量', prop: 'readNumber', visible: true },
        { label: '备注', prop: 'remark', visible: true },
        { label: '状态', prop: 'status', visible: true },
        { label: '创建时间', prop: 'createTime', visible: true },
        { label: '操作', prop: 'operation', visible: true }
      ],
    };
  },
  watch: {
    // 监听columns数组中每个对象的visible属性变化
    columns: {
      handler(newVal) {
        localStorage.setItem('article-table-columns', JSON.stringify(newVal));
      },
      deep: true // 深度监听
    }
  },
  created() {
    // 从localStorage恢复列显示状态
    const savedColumns = localStorage.getItem('article-table-columns');
    if (savedColumns) {
      try {
        const parsedColumns = JSON.parse(savedColumns);
        this.columns = this.columns.map((col, index) => ({
          ...col,
          visible: parsedColumns[index]?.visible ?? true
        }));
      } catch (e) {
        console.error('Failed to parse saved columns:', e);
      }
    }
    this.getList();
  },
  methods: {
    onCopyError(){
      this.$message.error("复制失败");
    },
    onCopySuccess(){
      this.$message.success("复制成功");
    },

    /** 查询推文列表 */
    getList() {
      this.loading = true;
      listArticle(this.queryParams).then(response => {
        this.articleList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        id: null,
        title: null,
        url: null,
        remark: null,
        status: null,
        createTime: null,
        updateTime: null
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.id)
      this.single = selection.length!==1
      this.multiple = !selection.length
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.form.status = "0";
      this.open = true;
      this.title = "添加推文";
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      const id = row.id || this.ids
      getArticle(id).then(response => {
        this.form = response.data;
        this.open = true;
        this.title = "修改推文";
      });
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate(valid => {
        if (valid) {
          if (this.form.id != null) {
            updateArticle(this.form).then(response => {
              this.$modal.msgSuccess("修改成功");
              this.open = false;
              this.getList();
            });
          } else {
            addArticle(this.form).then(response => {
              this.$modal.msgSuccess("新增成功");
              this.open = false;
              this.getList();
            });
          }
        }
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const ids = row.id || this.ids;
      this.$modal.confirm('是否确认删除推文ID为"' + ids + '"的数据项？').then(function() {
        return delArticle(ids);
      }).then(() => {
        this.getList();
        this.$modal.msgSuccess("删除成功");
      }).catch(() => {});
    },
    /** 导出按钮操作 */
    handleExport() {
      this.$modal.confirm('是否确认导出所有推文数据？').then(() => {
        this.download('custom/article/export', {
          ...this.queryParams
        }, `article_${new Date().getTime()}.xlsx`)
      }).catch(() => {});
    },
    // 处理列显示/隐藏
    handleColumnCommand(command) {
      if (command === 'reset') {
        this.resetColumns();
        return;
      }
      const column = this.columns.find(item => item.prop === command);
      if (column) {
        column.visible = !column.visible;
      }
    },
    // 重置列设置
    resetColumns() {
      this.columns.forEach(col => col.visible = true);
      localStorage.removeItem('article-table-columns');
      this.$message.success('列设置已重置');
    },
  }
};
</script>

<style scoped>
.el-dropdown {
  vertical-align: top;
}
.el-dropdown + .el-dropdown {
  margin-left: 10px;
}
.el-icon-arrow-down {
  font-size: 12px;
}
</style>
