package com.ruoyi.custom.service.impl;

import java.util.List;
import com.ruoyi.common.utils.DateUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.ruoyi.custom.mapper.ExchangeRecordMapper;
import com.ruoyi.custom.domain.ExchangeRecord;
import com.ruoyi.custom.service.IExchangeRecordService;

/**
 * 兑换记录Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-01-02
 */
@Service
public class ExchangeRecordServiceImpl implements IExchangeRecordService 
{
    @Autowired
    private ExchangeRecordMapper exchangeRecordMapper;

    /**
     * 查询兑换记录
     * 
     * @param id 兑换记录主键
     * @return 兑换记录
     */
    @Override
    public ExchangeRecord selectExchangeRecordById(Long id)
    {
        return exchangeRecordMapper.selectExchangeRecordById(id);
    }

    /**
     * 查询兑换记录列表
     * 
     * @param exchangeRecord 兑换记录
     * @return 兑换记录
     */
    @Override
    public List<ExchangeRecord> selectExchangeRecordList(ExchangeRecord exchangeRecord)
    {
        return exchangeRecordMapper.selectExchangeRecordList(exchangeRecord);
    }

    /**
     * 新增兑换记录
     * 
     * @param exchangeRecord 兑换记录
     * @return 结果
     */
    @Override
    public int insertExchangeRecord(ExchangeRecord exchangeRecord)
    {
        exchangeRecord.setCreateTime(DateUtils.getNowDate());
        return exchangeRecordMapper.insertExchangeRecord(exchangeRecord);
    }

    /**
     * 修改兑换记录
     * 
     * @param exchangeRecord 兑换记录
     * @return 结果
     */
    @Override
    public int updateExchangeRecord(ExchangeRecord exchangeRecord)
    {
        exchangeRecord.setUpdateTime(DateUtils.getNowDate());
        return exchangeRecordMapper.updateExchangeRecord(exchangeRecord);
    }

    /**
     * 批量删除兑换记录
     * 
     * @param ids 需要删除的兑换记录主键
     * @return 结果
     */
    @Override
    public int deleteExchangeRecordByIds(Long[] ids)
    {
        return exchangeRecordMapper.deleteExchangeRecordByIds(ids);
    }

    /**
     * 删除兑换记录信息
     * 
     * @param id 兑换记录主键
     * @return 结果
     */
    @Override
    public int deleteExchangeRecordById(Long id)
    {
        return exchangeRecordMapper.deleteExchangeRecordById(id);
    }

    @Override
    public int selectExchangeRecordByUidAndGoodsId(String openid, Long goodsId) {
        return exchangeRecordMapper.selectExchangeRecordByUidAndGoodsId(openid,goodsId);
    }
}
