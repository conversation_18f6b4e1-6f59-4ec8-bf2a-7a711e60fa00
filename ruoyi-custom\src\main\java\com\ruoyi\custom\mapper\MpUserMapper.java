package com.ruoyi.custom.mapper;

import java.util.List;
import com.ruoyi.custom.domain.MpUser;

/**
 * 微信公众号用户Mapper接口
 * 
 * <AUTHOR>
 * @date 2024-05-15
 */
public interface MpUserMapper 
{
    /**
     * 查询微信公众号用户
     * 
     * @param id 微信公众号用户主键
     * @return 微信公众号用户
     */
    public MpUser selectMpUserById(Long id);

    public MpUser selectMpUserByOpenid(String openid);

    /**
     * 查询微信公众号用户列表
     * 
     * @param mpUser 微信公众号用户
     * @return 微信公众号用户集合
     */
    public List<MpUser> selectMpUserList(MpUser mpUser);

    /**
     * 新增微信公众号用户
     * 
     * @param mpUser 微信公众号用户
     * @return 结果
     */
    public int insertMpUser(MpUser mpUser);

    /**
     * 修改微信公众号用户
     * 
     * @param mpUser 微信公众号用户
     * @return 结果
     */
    public int updateMpUser(MpUser mpUser);

    /**
     * 删除微信公众号用户
     * 
     * @param id 微信公众号用户主键
     * @return 结果
     */
    public int deleteMpUserById(Long id);

    /**
     * 批量删除微信公众号用户
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteMpUserByIds(Long[] ids);

    List<MpUser> selectMpUserByNicknameLike(String nickname);

    int selectMpUserAllCount();


    int selectMpUserIntegralAllCount();

}
