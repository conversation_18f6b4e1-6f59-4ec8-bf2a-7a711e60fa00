package com.ruoyi.custom.service.impl;

import java.util.*;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.stream.Collectors;

import cn.hutool.core.date.DateField;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import com.alibaba.fastjson2.JSONObject;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.utils.DateUtils;
import com.ruoyi.common.utils.PageUtils;
import com.ruoyi.common.utils.jwt.JwtUtil;
import com.ruoyi.custom.domain.Article;
import com.ruoyi.custom.domain.MpUser;
import com.ruoyi.custom.mapper.ArticleMapper;
import com.ruoyi.custom.mapper.MpUserMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.ruoyi.custom.mapper.IntegralRecordMapper;
import com.ruoyi.custom.domain.IntegralRecord;
import com.ruoyi.custom.service.IIntegralRecordService;
import org.springframework.util.ObjectUtils;

/**
 * 积分记录Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-05-15
 */
@Service
public class IntegralRecordServiceImpl implements IIntegralRecordService {
    @Autowired
    private IntegralRecordMapper integralRecordMapper;

    @Autowired
    private ArticleMapper articleMapper;

    @Autowired
    private MpUserMapper mpUserMapper;

    /**
     * 查询积分记录
     *
     * @param id 积分记录主键
     * @return 积分记录
     */
    @Override
    public IntegralRecord selectIntegralRecordById(Long id) {
        return integralRecordMapper.selectIntegralRecordById(id);
    }

    /**
     * 查询积分记录列表
     *
     * @param integralRecord 积分记录
     * @return 积分记录
     */
    @Override
    public List<IntegralRecord> selectIntegralRecordList(IntegralRecord integralRecord) {
        if (integralRecord.getNickname() != null) {
            List<MpUser> mpUserList = mpUserMapper.selectMpUserByNicknameLike(integralRecord.getNickname());
            if (!mpUserList.isEmpty()) {
                List<String> openIdList = mpUserList.stream().map(MpUser::getOpenid).collect(Collectors.toList());
                integralRecord.setOpenidList(openIdList);
            } else {
                integralRecord.setOpenidList(Collections.singletonList("-1"));
            }

        }

        PageUtils.startPage();
        List<IntegralRecord> integralRecords = integralRecordMapper.selectIntegralRecordList(integralRecord);
        integralRecords.forEach(item -> {
            Article article = articleMapper.selectArticleById(item.getArticleId());
            if (article != null) {
                item.setArticleTitle(article.getTitle());
            }
            MpUser mpUser = mpUserMapper.selectMpUserByOpenid(item.getOpenid());
            if (mpUser != null) {
                item.setNickname(mpUser.getNickname());
//                item.setAvatar(mpUser.getAvatar());
            }
        });
        return integralRecords;
    }
    @Override
    public List<IntegralRecord> selectIntegralRecordList2(IntegralRecord integralRecord) {
        if (integralRecord.getNickname() != null) {
            List<MpUser> mpUserList = mpUserMapper.selectMpUserByNicknameLike(integralRecord.getNickname());
            if (!mpUserList.isEmpty()) {
                List<String> openIdList = mpUserList.stream().map(MpUser::getOpenid).collect(Collectors.toList());
                integralRecord.setOpenidList(openIdList);
            } else {
                integralRecord.setOpenidList(Collections.singletonList("-1"));
            }
        }
        return integralRecordMapper.selectIntegralRecordList2(integralRecord);
    }

    /**
     * 新增积分记录
     *
     * @param integralRecord 积分记录
     * @return 结果
     */
    @Override
    public int insertIntegralRecord(IntegralRecord integralRecord) {
        integralRecord.setCreateTime(DateUtils.getNowDate());
        return integralRecordMapper.insertIntegralRecord(integralRecord);
    }

    /**
     * 修改积分记录
     *
     * @param integralRecord 积分记录
     * @return 结果
     */
    @Override
    public int updateIntegralRecord(IntegralRecord integralRecord) {
        integralRecord.setUpdateTime(DateUtils.getNowDate());
        return integralRecordMapper.updateIntegralRecord(integralRecord);
    }

    /**
     * 批量删除积分记录
     *
     * @param ids 需要删除的积分记录主键
     * @return 结果
     */
    @Override
    public int deleteIntegralRecordByIds(Long[] ids) {
        return integralRecordMapper.deleteIntegralRecordByIds(ids);
    }

    /**
     * 删除积分记录信息
     *
     * @param id 积分记录主键
     * @return 结果
     */
    @Override
    public int deleteIntegralRecordById(Long id) {
        return integralRecordMapper.deleteIntegralRecordById(id);
    }

    @Override
    public AjaxResult integralList(String token) {
        // 验证token是否有效
        if (!JwtUtil.validate(token)) {
            return AjaxResult.error("token失效,请重新进入！");
        }
        String openid = JwtUtil.getJSONObject(token).getStr("id");
        return AjaxResult.success(integralRecordMapper.selectIntegralRecordListByOpenid(openid));
    }

    @Override
    public AjaxResult oneWeekPoints(String dateRange) {
        // 生成过去一周的日期列表
        List<String> dateTimeList = new ArrayList<>();
        if (ObjectUtils.isEmpty(dateRange) || dateRange.equals("null")) {
            dateTimeList = DateUtil.rangeToList(DateUtil.date().offset(DateField.DAY_OF_YEAR, -6), new Date(), DateField.DAY_OF_YEAR)
                    .stream()
                    .map(item -> DateUtil.format(item, "yyyy-MM-dd"))
                    .collect(Collectors.toList());
        } else {
            List<DateTime> dateTimeCollect = Arrays.stream(dateRange.split(",")).map(DateUtil::parse).collect(Collectors.toList());
            dateTimeList =
                    DateUtil.rangeToList(dateTimeCollect.get(0), dateTimeCollect.get(1), DateField.DAY_OF_YEAR)
                            .stream()
                            .map(item -> DateUtil.format(item, "yyyy-MM-dd"))
                            .collect(Collectors.toList());
        }

        // 查询过去一周的积分记录
        List<JSONObject> jsonObjects = integralRecordMapper.oneWeekPoints(dateTimeList);

        // 将积分记录转换为日期-积分的映射
        Map<String, String> pointMap = jsonObjects.stream()
                .collect(Collectors.toMap(
                        obj -> DateUtil.format(DateUtil.parse(obj.getString("datetime")), "yyyy-MM-dd"),  // 以日期为键
                        obj -> obj.getString("number"),  // 积分数为值
                        (existing, replacement) -> existing  // 处理重复的日期情况
                ));

        // 根据日期列表生成对应的积分列表
        List<String> integralList = dateTimeList.stream()
                .map(date -> pointMap.getOrDefault(date, "0"))  // 如果没有对应的积分记录则默认为0
                .collect(Collectors.toList());

        // 构建结果对象并返回
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("integralTimeData", dateTimeList);
        jsonObject.put("integralNumberData", integralList);
        return AjaxResult.success(jsonObject);
    }

    @Override
    public List<JSONObject> numberOfViewers(List<String> dateRangeList) {
        return integralRecordMapper.numberOfViewers(dateRangeList);
    }

    @Override
    public List<JSONObject> numberOfPointsObtained(List<String> dateRangeList) {
        return integralRecordMapper.numberOfPointsObtained(dateRangeList);
    }

}
