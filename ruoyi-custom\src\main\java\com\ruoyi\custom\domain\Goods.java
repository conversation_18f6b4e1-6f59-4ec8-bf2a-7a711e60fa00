package com.ruoyi.custom.domain;

import com.fasterxml.jackson.annotation.JsonFormat;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;
import org.apache.poi.ss.usermodel.HorizontalAlignment;

import java.util.Date;

/**
 * 商品对象 tbl_goods
 * 
 * <AUTHOR>
 * @date 2024-05-16
 */
public class Goods extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** $column.columnComment */
    private Long id;

    /** 商品标题 */
    @Excel(name = "商品标题",width = 30, align = HorizontalAlignment.LEFT)
    private String title;

    /** 积分 */
    @Excel(name = "积分")
    private Long integral;

    /** 首图 */
    private String pic;

    /** 图片介绍 */
    private String pics;

    /** 详情 */
    private String detail;

    /** 状态 */
    @Excel(name = "状态", readConverterExp = "0=正常,1=停用")
    private String status;

    /** 库存 */
    @Excel(name = "库存")
    private Long stock;

    /** 销量 */
    @Excel(name = "销量")
    private Long salesVolume;


    /** 是否启用自动上架 */
    @Excel(name = "是否启用自动上架", readConverterExp = "true=启用,false=停用")
    private String enableAutoOnShelf;

    /** 上架方式 */
    @Excel(name = "上架方式", readConverterExp = "weekly=每周,specific=指定时间")
    private String onShelfType;

    /** 每周几上架 */
    @Excel(name = "每周几上架")
    private String weekDay;

    /** 每周上架时间 */
    @Excel(name = "每周上架时间")
    private String weekTime;

    /** 指定上架时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "指定上架时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date specificTime;

    /** 上架数量 */
    @Excel(name = "上架数量")
    private Long onShelfQuantity;

    private String isAutoShelved;

    public String getIsAutoShelved() {
        return isAutoShelved;
    }

    public void setIsAutoShelved(String isAutoShelved) {
        this.isAutoShelved = isAutoShelved;
    }

    public String getEnableAutoOnShelf() {
        return enableAutoOnShelf;
    }

    public void setEnableAutoOnShelf(String enableAutoOnShelf) {
        this.enableAutoOnShelf = enableAutoOnShelf;
    }

    public String getOnShelfType() {
        return onShelfType;
    }

    public void setOnShelfType(String onShelfType) {
        this.onShelfType = onShelfType;
    }

    public String getWeekDay() {
        return weekDay;
    }

    public void setWeekDay(String weekDay) {
        this.weekDay = weekDay;
    }

    public String getWeekTime() {
        return weekTime;
    }

    public void setWeekTime(String weekTime) {
        this.weekTime = weekTime;
    }

    public Date getSpecificTime() {
        return specificTime;
    }

    public void setSpecificTime(Date specificTime) {
        this.specificTime = specificTime;
    }

    public Long getOnShelfQuantity() {
        return onShelfQuantity;
    }

    public void setOnShelfQuantity(Long onShelfQuantity) {
        this.onShelfQuantity = onShelfQuantity;
    }

    public void setId(Long id)
    {
        this.id = id;
    }

    public Long getId() 
    {
        return id;
    }
    public void setTitle(String title) 
    {
        this.title = title;
    }

    public String getTitle() 
    {
        return title;
    }
    public void setIntegral(Long integral) 
    {
        this.integral = integral;
    }

    public Long getIntegral() 
    {
        return integral;
    }
    public void setPic(String pic) 
    {
        this.pic = pic;
    }

    public String getPic() 
    {
        return pic;
    }
    public void setPics(String pics) 
    {
        this.pics = pics;
    }

    public String getPics() 
    {
        return pics;
    }
    public void setDetail(String detail) 
    {
        this.detail = detail;
    }

    public String getDetail() 
    {
        return detail;
    }
    public void setStatus(String status) 
    {
        this.status = status;
    }

    public String getStatus() 
    {
        return status;
    }
    public void setStock(Long stock) 
    {
        this.stock = stock;
    }

    public Long getStock() 
    {
        return stock;
    }
    public void setSalesVolume(Long salesVolume) 
    {
        this.salesVolume = salesVolume;
    }

    public Long getSalesVolume() 
    {
        return salesVolume;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("id", getId())
            .append("title", getTitle())
            .append("integral", getIntegral())
            .append("pic", getPic())
            .append("pics", getPics())
            .append("detail", getDetail())
            .append("remark", getRemark())
            .append("status", getStatus())
            .append("stock", getStock())
            .append("salesVolume", getSalesVolume())
            .append("createTime", getCreateTime())
            .append("updateTime", getUpdateTime())
                .append("isAutoShelved", getIsAutoShelved())
                .append("enableAutoOnShelf", getEnableAutoOnShelf())
                .append("onShelfType", getOnShelfType())
                .append("weekDay", getWeekDay())
                .append("weekTime", getWeekTime())
                .append("specificTime", getSpecificTime())
                .append("onShelfQuantity", getOnShelfQuantity())
            .toString();
    }
}
