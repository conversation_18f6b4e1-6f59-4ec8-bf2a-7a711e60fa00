package com.ruoyi.custom.service.impl;

import java.util.List;
import java.util.Random;

import cn.hutool.core.util.RandomUtil;
import com.alibaba.fastjson2.JSONObject;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.utils.DateUtils;
import com.ruoyi.common.utils.jwt.JwtUtil;
import lombok.extern.slf4j.Slf4j;
import me.chanjar.weixin.common.bean.oauth2.WxOAuth2AccessToken;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.ruoyi.custom.mapper.MpUserMapper;
import com.ruoyi.custom.domain.MpUser;
import com.ruoyi.custom.service.IMpUserService;


/**
 * 微信公众号用户Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-05-15
 */
@Slf4j
@Service
public class MpUserServiceImpl implements IMpUserService {
    @Autowired
    private MpUserMapper mpUserMapper;

    /**
     * 查询微信公众号用户
     *
     * @param id 微信公众号用户主键
     * @return 微信公众号用户
     */
    @Override
    public MpUser selectMpUserById(Long id) {
        return mpUserMapper.selectMpUserById(id);
    }

    /**
     * 查询微信公众号用户列表
     *
     * @param mpUser 微信公众号用户
     * @return 微信公众号用户
     */
    @Override
    public List<MpUser> selectMpUserList(MpUser mpUser) {
        return mpUserMapper.selectMpUserList(mpUser);
    }

    /**
     * 新增微信公众号用户
     *
     * @param mpUser 微信公众号用户
     * @return 结果
     */
    @Override
    public int insertMpUser(MpUser mpUser) {
        mpUser.setCreateTime(DateUtils.getNowDate());
        return mpUserMapper.insertMpUser(mpUser);
    }

    /**
     * 修改微信公众号用户
     *
     * @param mpUser 微信公众号用户
     * @return 结果
     */
    @Override
    public int updateMpUser(MpUser mpUser) {
        MpUser mu = mpUserMapper.selectMpUserById(mpUser.getId());
        if (mu != null) {
            log.info("【修改用户】修改前信息：{}，修改后：{}", mu.toString(), mpUser.toString());
        }
        mpUser.setUpdateTime(DateUtils.getNowDate());
        return mpUserMapper.updateMpUser(mpUser);
    }

    /**
     * 批量删除微信公众号用户
     *
     * @param ids 需要删除的微信公众号用户主键
     * @return 结果
     */
    @Override
    public int deleteMpUserByIds(Long[] ids) {
        return mpUserMapper.deleteMpUserByIds(ids);
    }

    /**
     * 删除微信公众号用户信息
     *
     * @param id 微信公众号用户主键
     * @return 结果
     */
    @Override
    public int deleteMpUserById(Long id) {
        return mpUserMapper.deleteMpUserById(id);
    }

    @Override
    public String checkMpUserExist(WxOAuth2AccessToken wxOAuth2AccessToken) {
        MpUser mpUser = mpUserMapper.selectMpUserByOpenid(wxOAuth2AccessToken.getOpenId());
        log.info("【用户登陆】用户openid:{}", wxOAuth2AccessToken.getOpenId());
        if (mpUser == null) {
            mpUser = new MpUser();
            mpUser.setOpenid(wxOAuth2AccessToken.getOpenId());
            mpUser.setUnionid(wxOAuth2AccessToken.getUnionId());
            mpUser.setCreateTime(DateUtils.getNowDate());
            mpUser.setLastTime(DateUtils.getNowDate());
            mpUser.setNickname("微信用户" + RandomUtil.randomStringUpper(6));
            mpUserMapper.insertMpUser(mpUser);
        } else {
            mpUser.setLastTime(DateUtils.getNowDate());
            mpUserMapper.updateMpUser(mpUser);
        }
        return JwtUtil.createToken(mpUser.getOpenid(), mpUser.getNickname());
    }

    @Override
    public AjaxResult mpUserInfo(String token) {
        // 验证token是否有效
        if (!JwtUtil.validate(token)) {
            return AjaxResult.error("token失效,请重新进入！");
        }
        String openid = JwtUtil.getJSONObject(token).getStr("id");

        // 查询用户信息
        MpUser mpUser = mpUserMapper.selectMpUserByOpenid(openid);
        if (mpUser == null) {
            return AjaxResult.error("用户信息获取失败！");
        }

        if (!mpUser.getStatus().equals("0")) {
            return AjaxResult.error("用户状态异常！");
        }

        JSONObject jsonObject = new JSONObject();
        jsonObject.put("nickname", mpUser.getNickname());
        jsonObject.put("avatar", mpUser.getAvatar());
        jsonObject.put("integral", mpUser.getIntegral());
        return AjaxResult.success(jsonObject);
    }

    @Override
    public AjaxResult updUserInfo(String token, String nickname, String avatar) {
        // 验证token是否有效
        if (!JwtUtil.validate(token)) {
            return AjaxResult.error("token失效,请重新进入！");
        }
        String openid = JwtUtil.getJSONObject(token).getStr("id");

        // 查询用户信息
        MpUser mpUser = mpUserMapper.selectMpUserByOpenid(openid);
        if (mpUser == null) {
            return AjaxResult.error("用户信息获取失败！");
        }

        if (!mpUser.getStatus().equals("0")) {
            return AjaxResult.error("用户状态异常！");
        }
        mpUser.setAvatar(avatar);
        mpUser.setNickname(nickname);
        int i = mpUserMapper.updateMpUser(mpUser);
        if (i > 0) {
            return AjaxResult.success("更新成功！");
        }
        return AjaxResult.error("更新失败，请稍后再试！");
    }

    @Override
    public int selectMpUserAllCount() {
        return mpUserMapper.selectMpUserAllCount();
    }

    @Override
    public int selectMpUserIntegralAllCount() {

        return mpUserMapper.selectMpUserIntegralAllCount();
    }
}
