package com.ruoyi.custom.service;

import java.util.List;

import com.alibaba.fastjson2.JSONObject;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.custom.domain.Order;

/**
 * 订单Service接口
 * 
 * <AUTHOR>
 * @date 2024-05-16
 */
public interface IOrderService 
{
    /**
     * 查询订单
     * 
     * @param id 订单主键
     * @return 订单
     */
    public Order selectOrderById(Long id);

    /**
     * 查询订单列表
     * 
     * @param order 订单
     * @return 订单集合
     */
    public List<Order> selectOrderList(Order order);

    /**
     * 新增订单
     * 
     * @param order 订单
     * @return 结果
     */
    public int insertOrder(Order order);

    /**
     * 修改订单
     * 
     * @param order 订单
     * @return 结果
     */
    public int updateOrder(Order order);

    /**
     * 批量删除订单
     * 
     * @param ids 需要删除的订单主键集合
     * @return 结果
     */
    public int deleteOrderByIds(Long[] ids);

    /**
     * 删除订单信息
     * 
     * @param id 订单主键
     * @return 结果
     */
    public int deleteOrderById(Long id);

    AjaxResult orderStatus(Long oId);

    AjaxResult exchangeGoods(String token, Long goodsId);

    AjaxResult orderList(String token);

    int selectOrderAllCount();

    List<JSONObject> numberOfExchanges(List<String> dateRangeList);

    List<JSONObject> numberOfSales(List<String> dateRangeList);

    List<JSONObject> selectOrderByIds(List<String> strings);

}
