package com.ruoyi.custom.controller.web;

import com.alibaba.fastjson2.JSONArray;
import com.alibaba.fastjson2.JSONObject;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.client.RestTemplate;


/**
 * @Author: y
 * @CreateDate: 2024/10/24 17:07
 * @Version: 1.0
 * @Description:
 */
@Controller
public class XunFeiController {

    @RequestMapping("/q/{str}")
    @ResponseBody
    public String test(@PathVariable String str){
        // 创建 RestTemplate 实例
        RestTemplate restTemplate = new RestTemplate();

        // 设置请求头
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_JSON);
        headers.set("Authorization", "Bearer eUsAkbJcvgpfASkrGZHo:komPsBsHcgAPVjEhRTat");

        // 创建请求体
        JSONObject requestBody = new JSONObject();
        requestBody.put("model", "lite");

        // 创建 messages 数组
        JSONObject systemMessage = new JSONObject();
        systemMessage.put("role", "system");
        systemMessage.put("content", "");

        JSONObject userMessage = new JSONObject();
        userMessage.put("role", "user");
        userMessage.put("content", str);

        // 将消息数组添加到请求体
        requestBody.put("messages", new JSONObject[]{systemMessage, userMessage});
        requestBody.put("stream", false);
        // 封装请求体和头部
        HttpEntity<String> entity = new HttpEntity<>(requestBody.toJSONString(), headers);

        // 发送 POST 请求
        ResponseEntity<String> response = restTemplate.postForEntity(
                "https://spark-api-open.xf-yun.com/v1/chat/completions",
                entity,
                String.class);

        // 打印响应
//        System.out.println("Response Status Code: " + response.getStatusCode());
//        System.out.println("Response Body: " + response.getBody());

        JSONObject jsonObject = JSONObject.parseObject(response.getBody());
        assert jsonObject != null;
        JSONArray choices = jsonObject.getJSONArray("choices");
        JSONObject choice = choices.getJSONObject(0);
        JSONObject message = choice.getJSONObject("message");
        return message.getString("content");
    }




}
