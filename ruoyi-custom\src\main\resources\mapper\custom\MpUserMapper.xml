<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.custom.mapper.MpUserMapper">
    
    <resultMap type="MpUser" id="MpUserResult">
        <result property="id"    column="id"    />
        <result property="nickname"    column="nickname"    />
        <result property="avatar"    column="avatar"    />
        <result property="gender"    column="gender"    />
        <result property="openid"    column="openid"    />
        <result property="unionid"    column="unionid"    />
        <result property="integral"    column="integral"    />
        <result property="phone"    column="phone"    />
        <result property="remark"    column="remark"    />
        <result property="status"    column="status"    />
        <result property="lastTime"    column="last_time"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateTime"    column="update_time"    />
    </resultMap>

    <sql id="selectMpUserVo">
        select id, nickname, avatar, gender, openid, unionid, integral, phone, remark, status, last_time, create_time, update_time from tbl_mp_user
    </sql>

    <select id="selectMpUserList" parameterType="MpUser" resultMap="MpUserResult">
        <include refid="selectMpUserVo"/>
        <where>  
            <if test="nickname != null  and nickname != ''"> and nickname like concat('%', #{nickname}, '%')</if>
            <if test="avatar != null  and avatar != ''"> and avatar = #{avatar}</if>
            <if test="gender != null "> and gender = #{gender}</if>
            <if test="openid != null  and openid != ''"> and openid = #{openid}</if>
            <if test="unionid != null  and unionid != ''"> and unionid = #{unionid}</if>
            <if test="integral != null "> and integral = #{integral}</if>
            <if test="phone != null  and phone != ''"> and phone like concat('%', #{phone}, '%')</if>
            <if test="status != null "> and status = #{status}</if>
            <if test="lastTime != null "> and last_time = #{lastTime}</if>
            <if test="remark != null and remark != ''"> and remark like concat('%', #{remark}, '%')</if>
        </where>
        order by id desc
    </select>
    
    <select id="selectMpUserById" parameterType="Long" resultMap="MpUserResult">
        <include refid="selectMpUserVo"/>
        where id = #{id}
    </select>
    <select id="selectMpUserByOpenid" parameterType="String" resultMap="MpUserResult">
        <include refid="selectMpUserVo"/>
        where openid = #{openid}
    </select>
    <select id="selectMpUserByNicknameLike" parameterType="String" resultMap="MpUserResult">
        <include refid="selectMpUserVo"/>
        where nickname like concat('%', #{nickname}, '%')
    </select>
    <select id="selectMpUserAllCount" resultType="java.lang.Integer">
        SELECT COUNT(*) number FROM `tbl_mp_user`
    </select>
    <select id="selectMpUserIntegralAllCount" resultType="java.lang.Integer">
        SELECT COALESCE
               ( sum( integral ), 0 ) number
        FROM
            `tbl_mp_user`
    </select>
    <insert id="insertMpUser" parameterType="MpUser" useGeneratedKeys="true" keyProperty="id">
        insert into tbl_mp_user
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="nickname != null">nickname,</if>
            <if test="avatar != null">avatar,</if>
            <if test="gender != null">gender,</if>
            <if test="openid != null and openid != ''">openid,</if>
            <if test="unionid != null">unionid,</if>
            <if test="integral != null">integral,</if>
            <if test="phone != null">phone,</if>
            <if test="remark != null">remark,</if>
            <if test="status != null">status,</if>
            <if test="lastTime != null">last_time,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateTime != null">update_time,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="nickname != null">#{nickname},</if>
            <if test="avatar != null">#{avatar},</if>
            <if test="gender != null">#{gender},</if>
            <if test="openid != null and openid != ''">#{openid},</if>
            <if test="unionid != null">#{unionid},</if>
            <if test="integral != null">#{integral},</if>
            <if test="phone != null">#{phone},</if>
            <if test="remark != null">#{remark},</if>
            <if test="status != null">#{status},</if>
            <if test="lastTime != null">#{lastTime},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateTime != null">#{updateTime},</if>
         </trim>
    </insert>

    <update id="updateMpUser" parameterType="MpUser">
        update tbl_mp_user
        <trim prefix="SET" suffixOverrides=",">
            <if test="nickname != null">nickname = #{nickname},</if>
            <if test="avatar != null">avatar = #{avatar},</if>
            <if test="gender != null">gender = #{gender},</if>
            <if test="openid != null and openid != ''">openid = #{openid},</if>
            <if test="unionid != null">unionid = #{unionid},</if>
            <if test="integral != null">integral = #{integral},</if>
            <if test="phone != null">phone = #{phone},</if>
            <if test="remark != null">remark = #{remark},</if>
            <if test="status != null">status = #{status},</if>
            <if test="lastTime != null">last_time = #{lastTime},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteMpUserById" parameterType="Long">
        delete from tbl_mp_user where id = #{id}
    </delete>

    <delete id="deleteMpUserByIds" parameterType="String">
        delete from tbl_mp_user where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>