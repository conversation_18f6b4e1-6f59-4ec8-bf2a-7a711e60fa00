package com.ruoyi.custom.controller.web;

import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.custom.service.IGoodsService;
import com.ruoyi.custom.service.IIntegralRecordService;
import com.ruoyi.custom.service.IMpUserService;
import com.ruoyi.custom.service.IOrderService;
import com.ruoyi.system.service.ISysConfigService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

/**
 * @Author: y
 * @CreateDate: 2024/5/16 16:18
 * @Version: 1.0
 * @Description:
 */
@RestController
@RequestMapping("/web")
public class WebIndexController {

    @Autowired
    private IMpUserService mpUserService;

    @Autowired
    private IIntegralRecordService integralRecordService;

    @Autowired
    private IOrderService orderService;

    @Autowired
    private IGoodsService goodsService;

    @Autowired
    private ISysConfigService sysConfigService;

    /**
     * 获取用户基本信息
     * @param token
     * @return
     */
    @PostMapping("/mpUserInfo")
    public AjaxResult mpUserInfo(@RequestParam("token") String token) {
        return mpUserService.mpUserInfo(token);
    }

    /**
     * 修改用户头像，昵称
     * @param token
     * @param nickname
     * @param avatar
     * @return
     */
    @PostMapping("/updUserInfo")
    public AjaxResult updUserInfo(@RequestParam("token") String token,
                                  @RequestParam(value = "nickname") String nickname,
                                  @RequestParam(value = "avatar",required = false) String avatar) {
        return mpUserService.updUserInfo(token, nickname, avatar);
    }

    /**
     * 获取用户积分明细
     */
    @PostMapping("/integralList")
    public AjaxResult integralList(@RequestParam("token") String token) {
        return integralRecordService.integralList(token);
    }

    /**
     * 获取兑换记录
     */
    @PostMapping("/orderList")
    public AjaxResult orderList(@RequestParam("token") String token) {
        return orderService.orderList(token);
    }

    /**
     * 获取商品列表
     */
    @PostMapping("/goodsList")
    public AjaxResult goodsList() {
        return goodsService.goodsList();
    }

    /**
     * 获取商品列表
     */
    @PostMapping("/integralRule")
    public AjaxResult integralRule() {
        String integralNumber = sysConfigService.selectConfigByKey("custom.integral.rule");
        return AjaxResult.success(integralNumber);
    }

}
