package com.ruoyi.custom.controller.web;

import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.utils.DateUtils;
import com.ruoyi.custom.service.IOrderService;
import com.ruoyi.custom.service.WorkdayCalculatorService;
import com.ruoyi.system.service.ISysConfigService;
import lombok.extern.slf4j.Slf4j;
import org.apache.poi.ss.formula.atp.WorkdayCalculator;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.web.bind.annotation.*;

import java.util.Date;

/**
 * @Author: y
 * @CreateDate: 2024/5/16 15:13
 * @Version: 1.0
 * @Description:
 */
@Slf4j
@RestController
@RequestMapping("/web")
public class WebOrderController {

    @Autowired
    private IOrderService orderService;

    @Autowired
    private ISysConfigService sysConfigService;

    @Autowired
    private WorkdayCalculatorService workdayCalculator;
    /**
     * 积分兑换商品
     * @param token
     * @param goodsId
     * @return
     */
    @PostMapping("/order/exchangeGoods")
    public AjaxResult exchangeGoods(@RequestParam("token") String token, @RequestParam("goodsId") Long goodsId){
        return orderService.exchangeGoods(token,goodsId);
    }

    @Scheduled(cron = "0 1 0 * * ?")
    public void getNextWorkday() {
        log.info("【定时任务】开始执行获取下一个工作日任务");
        long startTime = System.currentTimeMillis();
        try {
            // 获取配置的工作日期限
            String dateLimit = sysConfigService.selectConfigByKey("custom.goods.delivery.date.limit");
            // 获取当前时间
            Date now = DateUtils.getNowDate();
            // 计算下一个工作日
            String nextWorkday = workdayCalculator.getWeekdayLimit(now, dateLimit);
            
            log.info("【定时任务】执行详情：\n" +
                    "当前时间：{}\n" +
                    "工作日期限：{} 个工作日\n" +
                    "计算得到的送货日期：{}\n" +
                    "任务耗时：{}毫秒", 
                    DateUtils.parseDateToStr(DateUtils.YYYY_MM_DD_HH_MM_SS, now),
                    dateLimit,
                    nextWorkday,
                    System.currentTimeMillis() - startTime);
        } catch (Exception e) {
            log.error("【定时任务】获取下一个工作日异常", e);
        }
    }
}
