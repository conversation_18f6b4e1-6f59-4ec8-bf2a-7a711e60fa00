package com.ruoyi.custom.controller.web;

import cn.hutool.core.net.URLDecoder;
import cn.hutool.core.util.RandomUtil;
import com.ruoyi.common.constant.Constants;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.domain.R;
import com.ruoyi.custom.domain.MpUser;
import com.ruoyi.custom.service.IMpUserService;
import me.chanjar.weixin.common.bean.WxJsapiSignature;
import me.chanjar.weixin.common.bean.oauth2.WxOAuth2AccessToken;
import me.chanjar.weixin.common.error.WxErrorException;
import me.chanjar.weixin.mp.api.WxMpService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.*;

import java.nio.charset.Charset;
import java.nio.charset.StandardCharsets;


/**
 * @Author: y
 * @CreateDate: 2024/5/14 10:10
 * @Version: 1.0
 * @Description:
 */
@Controller
@RequestMapping("/mp")
public class MPController {

    @Autowired
    private WxMpService wxMpService;

    @Autowired
    private IMpUserService mpUserService;

    @Value("${wechat.mp.baseUrl}")
    private String baseUrl;

    @ResponseBody
    @PostMapping("/authUrl")
    public AjaxResult authUrl(@RequestParam("path") String path) {
        String url = String.format("https://open.weixin.qq.com/connect/oauth2/authorize?appid=%s&redirect_uri=%s&response_type=code&" +
                "scope=snsapi_userinfo&state=%s#wechat_redirect", wxMpService.getWxMpConfigStorage().getAppId(), URLDecoder.decode(baseUrl + "prod-api/mp/auth", StandardCharsets.UTF_8), path);
        return AjaxResult.success(url);
    }

    @RequestMapping("/auth")
    public String auth(@RequestParam("code") String code, @RequestParam("state") String path) throws WxErrorException {
        WxOAuth2AccessToken wxOAuth2AccessToken = wxMpService.getOAuth2Service().getAccessToken(code);
        String token = mpUserService.checkMpUserExist(wxOAuth2AccessToken);
        path = URLDecoder.decode(path, StandardCharsets.UTF_8);
        return "redirect:" + baseUrl + "h5/pages/" + path + "?tk=" + token;
    }

    /**
     * 前端先需要从后台获取一些配置参数，比如appId、timestamp、nonceStr、signature，这里需要后端支持
     */
    @ResponseBody
    @PostMapping("getConfigData")
    public AjaxResult getConfigData(@RequestParam("url")String url) throws Exception {
        WxJsapiSignature wxJsapiSignature2 = this.wxMpService.createJsapiSignature(URLDecoder.decode(url, StandardCharsets.UTF_8));
        return AjaxResult.success(wxJsapiSignature2);
    }
}
