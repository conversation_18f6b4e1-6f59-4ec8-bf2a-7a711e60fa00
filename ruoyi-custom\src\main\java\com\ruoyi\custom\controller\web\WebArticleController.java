package com.ruoyi.custom.controller.web;

import com.ruoyi.common.annotation.Anonymous;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.custom.domain.Article;
import com.ruoyi.custom.service.IArticleService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * @Author: y
 * @CreateDate: 2024/5/14 17:23
 * @Version: 1.0
 * @Description:
 */
@Controller
@RequestMapping("/web")
public class WebArticleController {

    @Autowired
    private IArticleService articleService;

    @Value("${wechat.mp.baseUrl}")
    private String baseUrl;

    /**
     * 跳转文章
     * @param articleId
     * @return
     */
    @Anonymous
    @GetMapping("/article/{articleId}")
    public String findArticle(@PathVariable("articleId") String articleId) {
        Article article = new Article();
        article.setUrl(articleId);
        article.setStatus("0");
        List<Article> articleList = articleService.selectArticleList(article);
        if (articleList.isEmpty()){
            return "redirect:" + baseUrl + "h5/pages/view/404";
        }
        //跳转到获得积分页面
        return "redirect:" + baseUrl + "h5/pages/view/index_index?articleId=" + articleId;
    }

    /**
     * 阅读文章获得积分
     * @param token
     * @param articleId
     * @return
     */
    @Anonymous
    @ResponseBody
    @PostMapping("/article/readArticle")
    public AjaxResult readArticle(@RequestParam("token")String token, @RequestParam("articleId")String articleId) {
        return articleService.readArticle(token,articleId);
    }

}
