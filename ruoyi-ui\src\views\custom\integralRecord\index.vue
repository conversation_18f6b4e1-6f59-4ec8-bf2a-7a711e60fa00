<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" label-width="80px">
      <el-form-item label="推文标题" prop="articleId">
        <el-select v-model="queryParams.articleId" filterable clearable placeholder="请选择推文标题">
          <el-option
            v-for="(item,index) in articleSelectList"
            :key="index"
            :label="item.title"
            :value="item.id"
          ></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="获得日期" prop="operationTime">
        <el-date-picker clearable
          v-model="queryParams.operationTime"
          type="date"
          value-format="yyyy-MM-dd"
          placeholder="请选择日期">
        </el-date-picker>
      </el-form-item>
      <el-form-item label="昵称" prop="nickname">
        <el-input
          v-model="queryParams.nickname"
          placeholder="请输入昵称"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="openid" prop="openid">
        <el-input
          v-model="queryParams.openid"
          placeholder="请输入openid"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
<!--      <el-col :span="1.5">-->
<!--        <el-button-->
<!--          type="primary"-->
<!--          plain-->
<!--          icon="el-icon-plus"-->
<!--          size="mini"-->
<!--          @click="handleAdd"-->
<!--          v-hasPermi="['custom:integralRecord:add']"-->
<!--        >新增</el-button>-->
<!--      </el-col>-->
<!--      <el-col :span="1.5">-->
<!--        <el-button-->
<!--          type="success"-->
<!--          plain-->
<!--          icon="el-icon-edit"-->
<!--          size="mini"-->
<!--          :disabled="single"-->
<!--          @click="handleUpdate"-->
<!--          v-hasPermi="['custom:integralRecord:edit']"-->
<!--        >修改</el-button>-->
<!--      </el-col>-->
<!--      <el-col :span="1.5">-->
<!--        <el-button-->
<!--          type="danger"-->
<!--          plain-->
<!--          icon="el-icon-delete"-->
<!--          size="mini"-->
<!--          :disabled="multiple"-->
<!--          @click="handleDelete"-->
<!--          v-hasPermi="['custom:integralRecord:remove']"-->
<!--        >删除</el-button>-->
<!--      </el-col>-->
      <el-col :span="1.5">
        <el-button
          type="warning"
          plain
          icon="el-icon-download"
          size="mini"
          @click="handleExport"
          v-hasPermi="['custom:integralRecord:export']"
        >导出</el-button>
      </el-col>
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table border v-loading="loading" :data="integralRecordList" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column label="ID" align="center" prop="id" width="80"/>
      <el-table-column label="文章标题" header-align="center" align="left" prop="articleTitle" width="450" :show-overflow-tooltip="true"/>
      <el-table-column label="昵称" align="center" prop="nickname" width="150" :show-overflow-tooltip="true"/>
      <el-table-column label="微信用户openid" align="center" prop="openid" width="300" :show-overflow-tooltip="true"/>
      <el-table-column label="获取积分" align="center" prop="integralNumber"/>
      <el-table-column label="备注" align="center" prop="remark" width="300" :show-overflow-tooltip="true"/>
      <el-table-column label="获得时间" align="center" prop="operationTime" width="180">
        <template slot-scope="scope">
          <span>{{scope.row.operationTime}}</span>
        </template>
      </el-table-column>
<!--      <el-table-column label="操作" align="center" class-name="small-padding fixed-width">-->
<!--        <template slot-scope="scope">-->
<!--          <el-button-->
<!--            size="mini"-->
<!--            type="text"-->
<!--            icon="el-icon-edit"-->
<!--            @click="handleUpdate(scope.row)"-->
<!--            v-hasPermi="['custom:integralRecord:edit']"-->
<!--          >修改</el-button>-->
<!--          <el-button-->
<!--            size="mini"-->
<!--            type="text"-->
<!--            icon="el-icon-delete"-->
<!--            @click="handleDelete(scope.row)"-->
<!--            v-hasPermi="['custom:integralRecord:remove']"-->
<!--          >删除</el-button>-->
<!--        </template>-->
<!--      </el-table-column>-->
    </el-table>

    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 添加或修改积分记录对话框 -->
    <el-dialog :title="title" :visible.sync="open" width="500px" append-to-body>
      <el-form ref="form" :model="form" :rules="rules" label-width="80px">
        <el-form-item label="${comment}" prop="openid">
          <el-input v-model="form.openid" placeholder="请输入${comment}" />
        </el-form-item>
        <el-form-item label="${comment}" prop="articleId">
          <el-input v-model="form.articleId" placeholder="请输入${comment}" />
        </el-form-item>
        <el-form-item label="${comment}" prop="integralNumber">
          <el-input v-model="form.integralNumber" placeholder="请输入${comment}" />
        </el-form-item>
        <el-form-item label="${comment}" prop="remark">
          <el-input v-model="form.remark" placeholder="请输入${comment}" />
        </el-form-item>
        <el-form-item label="${comment}" prop="operationTime">
          <el-date-picker clearable
            v-model="form.operationTime"
            type="date"
            value-format="yyyy-MM-dd"
            placeholder="请选择${comment}">
          </el-date-picker>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { listIntegralRecord, getIntegralRecord, delIntegralRecord, addIntegralRecord, updateIntegralRecord } from "@/api/custom/integralRecord";
import {selectListArticle} from "../../../api/custom/article";

export default {
  name: "IntegralRecord",
  data() {
    return {
      articleSelectList:[],
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 积分记录表格数据
      integralRecordList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        openid: null,
        articleId: null,
        nickname: null,
        integralNumber: null,
        type: 0,
        operationTime: null,
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {
        openid: [
          { required: true, message: "$comment不能为空", trigger: "blur" }
        ],
        articleId: [
          { required: true, message: "$comment不能为空", trigger: "blur" }
        ],
        integralNumber: [
          { required: true, message: "$comment不能为空", trigger: "blur" }
        ],
        type: [
          { required: true, message: "默认0获得积分1消耗积分不能为空", trigger: "change" }
        ],
        remark: [
          { required: true, message: "$comment不能为空", trigger: "blur" }
        ],
        operationTime: [
          { required: true, message: "$comment不能为空", trigger: "blur" }
        ],
        createTime: [
          { required: true, message: "$comment不能为空", trigger: "blur" }
        ],
        updateTime: [
          { required: true, message: "$comment不能为空", trigger: "blur" }
        ]
      }
    };
  },
  created() {
    this.getList();
    this.getArticleSelect();
  },
  methods: {
    getArticleSelect() {
      selectListArticle().then(response => {
        this.articleSelectList = response.rows;
      });
    },
    /** 查询积分记录列表 */
    getList() {
      this.loading = true;
      listIntegralRecord(this.queryParams).then(response => {
        this.integralRecordList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        id: null,
        openid: null,
        articleId: null,
        integralNumber: null,
        type: null,
        remark: null,
        operationTime: null,
        createTime: null,
        updateTime: null
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.id)
      this.single = selection.length!==1
      this.multiple = !selection.length
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.open = true;
      this.title = "添加积分记录";
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      const id = row.id || this.ids
      getIntegralRecord(id).then(response => {
        this.form = response.data;
        this.open = true;
        this.title = "修改积分记录";
      });
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate(valid => {
        if (valid) {
          if (this.form.id != null) {
            updateIntegralRecord(this.form).then(response => {
              this.$modal.msgSuccess("修改成功");
              this.open = false;
              this.getList();
            });
          } else {
            addIntegralRecord(this.form).then(response => {
              this.$modal.msgSuccess("新增成功");
              this.open = false;
              this.getList();
            });
          }
        }
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const ids = row.id || this.ids;
      this.$modal.confirm('是否确认删除积分记录编号为"' + ids + '"的数据项？').then(function() {
        return delIntegralRecord(ids);
      }).then(() => {
        this.getList();
        this.$modal.msgSuccess("删除成功");
      }).catch(() => {});
    },
    /** 导出按钮操作 */
    handleExport() {
      this.$modal.confirm('是否确认导出查询出来的所有积分记录数据项？').then(() => {
        this.download('custom/integralRecord/export', {
          ...this.queryParams
        }, `integralRecord_${new Date().getTime()}.xlsx`, {
          timeout: 30000  // 设置超时时间为30秒
        })
      }).catch(() => {});
    }
  }
};
</script>
