<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" label-width="70px">
      <el-form-item label="商品标题" prop="title">
        <el-input
          v-model="queryParams.title"
          placeholder="请输入商品标题"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="primary"
          plain
          icon="el-icon-plus"
          size="mini"
          @click="handleAdd"
          v-hasPermi="['custom:goods:add']"
        >新增</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="success"
          plain
          icon="el-icon-edit"
          size="mini"
          :disabled="single"
          @click="handleUpdate"
          v-hasPermi="['custom:goods:edit']"
        >修改</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="danger"
          plain
          icon="el-icon-delete"
          size="mini"
          :disabled="multiple"
          @click="handleDelete"
          v-hasPermi="['custom:goods:remove']"
        >删除</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="warning"
          plain
          icon="el-icon-download"
          size="mini"
          @click="handleExport"
          v-hasPermi="['custom:goods:export']"
        >导出</el-button>
      </el-col>
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table border v-loading="loading" :data="goodsList" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column label="ID" align="center" prop="id" width="80"/>
      <el-table-column label="商品标题" header-align="center" align="left" prop="title" width="280" :show-overflow-tooltip="true"/>
      <el-table-column label="首图" align="center" prop="pic">
        <template slot-scope="scope">
          <div class="demo-image__preview">
            <el-image :key="index" v-for="(item,index) in scope.row.pic.split('|')"
                      style="width: 30px; height: 30px;border-radius: 5px;"
                      :src="VUE_APP_BASE_API + item"
                      :preview-src-list="[VUE_APP_BASE_API + item]">
            </el-image>
          </div>
        </template>
      </el-table-column>
      <el-table-column label="轮播图" header-align="center" align="left" prop="pics" width="200" :show-overflow-tooltip="true">
        <template slot-scope="scope">
          <div class="demo-image__preview">
            <el-image :key="index" v-for="(item,index) in scope.row.pics.split(',')"
                      style="width: 30px; height: 30px;border-radius: 5px;"
                      :src="VUE_APP_BASE_API + item"
                      :preview-src-list="[VUE_APP_BASE_API + item]">
            </el-image>
          </div>
        </template>
      </el-table-column>
      <el-table-column label="积分" align="center" prop="integral" />
      <el-table-column label="备注" align="center" prop="remark" width="150" :show-overflow-tooltip="true"/>
      <el-table-column label="状态" align="center" prop="status">
        <template slot-scope="scope">
          <el-tag v-if="scope.row.status === '0'" size="small" type="success">正常</el-tag>
          <el-tag v-else-if="scope.row.status === '1'" size="small" type="danger">下架</el-tag>
        </template>
      </el-table-column>
      <el-table-column label="库存" align="center" prop="stock" />
      <el-table-column label="销量" align="center" prop="salesVolume" />
      <el-table-column label="定时上架" align="center" prop="enableAutoOnShelf">
        <template slot-scope="scope">
          <el-tag v-if="scope.row.enableAutoOnShelf === 'true'" type="success">已开启</el-tag>
          <el-tag v-else type="info">未开启</el-tag>
        </template>
      </el-table-column>

      <el-table-column label="操作" align="center" class-name="small-padding fixed-width" fixed="right" width="160">
        <template slot-scope="scope">
          <el-button
            size="mini"
            type="text"
            icon="el-icon-edit"
            @click="handleUpdate(scope.row)"
            v-hasPermi="['custom:goods:edit']"
          >修改</el-button>
          <el-button
            size="mini"
            type="text"
            icon="el-icon-delete"
            @click="handleDelete(scope.row)"
            v-hasPermi="['custom:goods:remove']"
          >删除</el-button>
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 添加或修改商品对话框 -->
    <el-dialog :title="title" :visible.sync="open" width="35%" :close-on-click-modal="false" append-to-body>
      <el-form ref="form" :model="form" :rules="rules" label-width="80px">
        <el-form-item label="商品标题" prop="title">
          <el-input v-model="form.title" placeholder="请输入商品标题" />
        </el-form-item>
        <el-form-item label="首图" prop="pic">
          <image-upload :limit="1" v-model="form.pic"/>
        </el-form-item>
        <el-form-item label="轮播图" prop="pics">
          <image-upload :limit="5" v-model="form.pics"/>
        </el-form-item>
        <el-row>
          <el-col :span="12">
            <el-form-item label="积分" prop="integral">
              <el-input v-model="form.integral" type="number" min="0" placeholder="请输入积分" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="库存" prop="stock">
              <el-input v-model="form.stock" type="number" min="0" placeholder="请输入库存" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-form-item label="状态" prop="status">
          <el-radio-group v-model="form.status">
            <el-radio
              v-for="dict in dict.type.custom_goods_status"
              :key="dict.value"
              :label="dict.value"
            >{{dict.label}}</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="定时上架" prop="enableAutoOnShelf">
          <el-switch v-model="form.enableAutoOnShelf" @change="handleAutoOnShelfChange"/>
        </el-form-item>
        <el-card v-if="form.enableAutoOnShelf" class="auto-shelf-card" shadow="never">
          <el-form-item label="上架方式" prop="onShelfType">
            <el-radio-group v-model="form.onShelfType" size="small">
              <el-radio label="weekly">每周</el-radio>
              <el-radio label="specific">指定时间</el-radio>
            </el-radio-group>
          </el-form-item>
          <el-form-item v-if="form.onShelfType === 'weekly'" label="上架时间" prop="weeklyOnShelfTime">
            <el-col :span="11">
              <el-select v-model="form.weekDay" placeholder="请选择星期" style="width: 95%">
                <el-option label="星期一" value="1"/>
                <el-option label="星期二" value="2"/>
                <el-option label="星期三" value="3"/>
                <el-option label="星期四" value="4"/>
                <el-option label="星期五" value="5"/>
                <el-option label="星期六" value="6"/>
                <el-option label="星期日" value="7"/>
              </el-select>
            </el-col>
            <el-col :span="2" class="text-center">
              <span class="el-icon-time"></span>
            </el-col>
            <el-col :span="11">
              <el-time-picker
                v-model="form.weekTime"
                placeholder="选择时间"
                format="HH:mm:ss"
                value-format="HH:mm:ss"
                style="width: 95%"
              />
            </el-col>
          </el-form-item>
          <el-form-item v-if="form.onShelfType === 'specific'" label="上架时间" prop="specificOnShelfTime">
            <el-date-picker
              v-model="form.specificTime"
              type="datetime"
              placeholder="选择上架时间"
              value-format="yyyy-MM-dd HH:mm:ss"
              style="width: 50%"
            />
          </el-form-item>
          <el-form-item label="上架数量" prop="onShelfQuantity">
            <el-input-number
              v-model="form.onShelfQuantity"
              :min="1"
              placeholder="请输入上架数量"
              style="width: 200px"
            />
          </el-form-item>
        </el-card>
        <el-form-item label="详情" prop="detail">
          <editor v-model="form.detail" :min-height="192"/>
        </el-form-item>
        <el-form-item label="备注" prop="remark">
          <el-input v-model="form.remark" type="textarea" rows="3" placeholder="请输入备注" />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { listGoods, getGoods, delGoods, addGoods, updateGoods } from "@/api/custom/goods";

export default {
  name: "Goods",
  dicts: ['custom_goods_status'],
  data() {
    return {
      VUE_APP_BASE_API: process.env.VUE_APP_BASE_API,
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 商品表格数据
      goodsList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        title: null,
        integral: null,
        pic: null,
        pics: null,
        detail: null,
        status: null,
        stock: null,
        salesVolume: null,
      },
      // 表单参数
      form: {
        id: null,
        title: null,
        integral: null,
        pic: null,
        pics: null,
        detail: null,
        remark: null,
        status: "0",
        stock: null,
        salesVolume: null,
        createTime: null,
        updateTime: null,
        enableAutoOnShelf: false,
        onShelfType: 'weekly',
        weekDay: '1',
        weekTime: '',
        specificTime: '',
        onShelfQuantity: 1
      },
      // 表单校验
      rules: {
        title: [
          { required: true, message: "商品标题不能为空", trigger: "blur" }
        ],
        integral: [
          { required: true, message: "积分不能为空", trigger: "blur" }
        ],
        pic: [
          { required: true, message: "首图不能为空", trigger: "blur" }
        ],
        pics: [
          { required: true, message: "轮播图不能为空", trigger: "blur" }
        ],
        status: [
          { required: true, message: "状态不能为空", trigger: "change" }
        ],
        stock: [
          { required: true, message: "库存不能为空", trigger: "blur" }
        ],
        salesVolume: [
          { required: true, message: "销量不能为空", trigger: "blur" }
        ],
        onShelfQuantity: [
          { required: true, message: "上架数量不能为空", trigger: "blur" }
        ],
        weekDay: [
          { required: true, message: "请选择星期", trigger: "change" }
        ],
        weekTime: [
          { required: true, message: "请选择时间", trigger: "change" }
        ],
        specificTime: [
          { required: true, message: "请选择上架时间", trigger: "change" }
        ]
      }
    };
  },
  created() {
    this.getList();
  },
  methods: {
    /** 查询商品列表 */
    getList() {
      this.loading = true;
      listGoods(this.queryParams).then(response => {
        this.goodsList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        id: null,
        title: null,
        integral: null,
        pic: null,
        pics: null,
        detail: null,
        remark: null,
        status: "0",
        stock: null,
        salesVolume: null,
        createTime: null,
        updateTime: null,
        enableAutoOnShelf: false,
        onShelfType: 'weekly',
        weekDay: '1',
        weekTime: '',
        specificTime: '',
        onShelfQuantity: 1
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.id)
      this.single = selection.length!==1
      this.multiple = !selection.length
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.open = true;
      this.title = "添加商品";
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      const id = row.id || this.ids
      getGoods(id).then(response => {
        this.form = response.data;
        this.form.enableAutoOnShelf = this.form.enableAutoOnShelf === "true";
        this.open = true;
        this.title = "修改商品";
      });
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate(valid => {
        if (valid) {
          // 创建一个新对象来保存表单数据
          const submitData = {
            ...this.form,
            // 确保发送给后端的是字符串类型
            enableAutoOnShelf: this.form.enableAutoOnShelf.toString()
          };

          // 如果自动上架关闭，相关字段设置为 null
          if (!this.form.enableAutoOnShelf) {
            submitData.onShelfType = null;
            submitData.weekDay = null;
            submitData.weekTime = null;
            submitData.specificTime = null;
            submitData.onShelfQuantity = null;
          }

          if (this.form.id != null) {
            updateGoods(submitData).then(response => {
              this.$modal.msgSuccess("修改成功");
              this.open = false;
              this.getList();
            });
          } else {
            addGoods(submitData).then(response => {
              this.$modal.msgSuccess("新增成功");
              this.open = false;
              this.getList();
            });
          }
        }
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const ids = row.id || this.ids;
      this.$modal.confirm('是否确认删除商品编号为"' + ids + '"的数据项？').then(function() {
        return delGoods(ids);
      }).then(() => {
        this.getList();
        this.$modal.msgSuccess("删除成功");
      }).catch(() => {});
    },
    /** 导出按钮操作 */
    handleExport() {
      this.$modal.confirm('是否确认导出所有商品数据？').then(() => {
        this.download('custom/goods/export', {
          ...this.queryParams
        }, `goods_${new Date().getTime()}.xlsx`)
      }).catch(() => {});
    },
    // 处理定时上架开关变化
    handleAutoOnShelfChange(val) {
      if (!val) {
        // 关闭开关时，重置所有相关字段
        this.form.onShelfType = 'weekly';
        this.form.weekDay = '1';
        this.form.weekTime = '';
        this.form.specificTime = '';
        this.form.onShelfQuantity = 1;
      }
    }
  }
};
</script>

<style scoped>
.auto-shelf-card {
  margin: 10px 0;
  background-color: #fafafa;
}

.auto-shelf-card :deep(.el-card__body) {
  padding: 15px 20px;
}

/* 移除最后一个表单项的下边距 */
.auto-shelf-card :deep(.el-form-item:last-child) {
  margin-bottom: 0;
}
</style>
