package com.ruoyi.custom.controller;

import cn.hutool.core.date.DateField;
import cn.hutool.core.date.DateRange;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.RandomUtil;
import cn.hutool.jwt.JWT;
import cn.hutool.jwt.JWTHeader;
import cn.hutool.jwt.JWTUtil;
import cn.hutool.jwt.JWTValidator;
import com.ruoyi.common.utils.jwt.JwtUtil;
import io.jsonwebtoken.Jwts;

import java.text.DateFormat;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * @Author: y
 * @CreateDate: 2024/5/15 11:01
 * @Version: 1.0
 * @Description:
 */
public class TestController {


    public static void main(String[] args) {
        String token = JwtUtil.createToken("obNbasrFqLdj3yK-F0u2mshMPL_I", "c");
        // System.out.println(token);
        // 创建日期范围生成器
//        List<DateTime> dateTimes = DateUtil.rangeToList(DateUtil.date().offset(DateField.DAY_OF_YEAR,-6), new Date(), DateField.DAY_OF_YEAR);
//        for (DateTime dateTime : dateTimes) {
//            System.out.println(DateUtil.format(dateTime, "yyyy-MM-dd"));
//        }

        // for (int i = 0; i < 20; i++) {
        //     System.out.println("微信用户" + RandomUtil.randomStringUpper(8));
        // }
    }
}
