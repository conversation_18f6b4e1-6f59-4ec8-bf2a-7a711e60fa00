import request from '@/utils/request'

// 查询微信公众号用户列表
export function listMpuser(query) {
  return request({
    url: '/custom/mpuser/list',
    method: 'get',
    params: query
  })
}

// 查询微信公众号用户详细
export function getMpuser(id) {
  return request({
    url: '/custom/mpuser/' + id,
    method: 'get'
  })
}

// 新增微信公众号用户
export function addMpuser(data) {
  return request({
    url: '/custom/mpuser',
    method: 'post',
    data: data
  })
}

// 修改微信公众号用户
export function updateMpuser(data) {
  return request({
    url: '/custom/mpuser',
    method: 'put',
    data: data
  })
}

// 删除微信公众号用户
export function delMpuser(id) {
  return request({
    url: '/custom/mpuser/' + id,
    method: 'delete'
  })
}
