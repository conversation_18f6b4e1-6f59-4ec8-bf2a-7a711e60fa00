<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.custom.mapper.ArticleMapper">
    
    <resultMap type="Article" id="ArticleResult">
        <result property="id"    column="id"    />
        <result property="title"    column="title"    />
        <result property="url"    column="url"    />
        <result property="remark"    column="remark"    />
        <result property="status"    column="status"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateTime"    column="update_time"    />
        <result property="readNumber" column="read_number" />
    </resultMap>

    <sql id="selectArticleVo">
        select id, title, url, remark, status, create_time, update_time from tbl_article
    </sql>

    <select id="selectArticleList" parameterType="Article" resultMap="ArticleResult">
        SELECT 
            a.*,
            COALESCE(r.read_count, 0) as read_number
        FROM tbl_article a
        LEFT JOIN (
            SELECT article_id, COUNT(*) as read_count 
            FROM tbl_integral_record 
            WHERE type = 0 
            GROUP BY article_id
        ) r ON a.id = r.article_id
        <where>  
            <if test="title != null and title != ''">AND a.title like concat('%', #{title}, '%')</if>
            <if test="url != null and url != ''">AND a.url like concat('%', #{url}, '%')</if>
            <if test="status != null">AND a.status = #{status}</if>
        </where>
        ORDER BY a.id desc
    </select>
    
    <select id="selectArticleById" parameterType="Long" resultMap="ArticleResult">
        <include refid="selectArticleVo"/>
        where id = #{id}
    </select>

    <select id="selectArticleByUrlLike" parameterType="String" resultMap="ArticleResult">
        <include refid="selectArticleVo"/>
        where url like concat('%', #{articleId}, '%')
    </select>
    <select id="selectArticleAllCount" resultType="java.lang.Integer">
        SELECT COUNT(*) number FROM `tbl_article`
    </select>
    <select id="selectArticleListByDotLike"  parameterType="String" resultMap="ArticleResult">
        <include refid="selectArticleVo"/>
        WHERE
        id NOT IN (
        SELECT
        article_id
        FROM
        tbl_integral_record
        WHERE
        openid = #{openid}
        AND type = 0
        ORDER BY
        id DESC)
    </select>

    <insert id="insertArticle" parameterType="Article" useGeneratedKeys="true" keyProperty="id">
        insert into tbl_article
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="title != null and title != ''">title,</if>
            <if test="url != null and url != ''">url,</if>
            <if test="remark != null">remark,</if>
            <if test="status != null">status,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateTime != null">update_time,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="title != null and title != ''">#{title},</if>
            <if test="url != null and url != ''">#{url},</if>
            <if test="remark != null">#{remark},</if>
            <if test="status != null">#{status},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateTime != null">#{updateTime},</if>
         </trim>
    </insert>

    <update id="updateArticle" parameterType="Article">
        update tbl_article
        <trim prefix="SET" suffixOverrides=",">
            <if test="title != null and title != ''">title = #{title},</if>
            <if test="url != null and url != ''">url = #{url},</if>
            <if test="remark != null">remark = #{remark},</if>
            <if test="status != null">status = #{status},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteArticleById" parameterType="Long">
        delete from tbl_article where id = #{id}
    </delete>

    <delete id="deleteArticleByIds" parameterType="String">
        delete from tbl_article where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>