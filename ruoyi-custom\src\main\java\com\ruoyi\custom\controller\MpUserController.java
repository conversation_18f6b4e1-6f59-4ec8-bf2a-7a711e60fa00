package com.ruoyi.custom.controller;

import java.util.List;
import javax.servlet.http.HttpServletResponse;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.custom.domain.MpUser;
import com.ruoyi.custom.service.IMpUserService;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.common.core.page.TableDataInfo;

/**
 * 微信公众号用户Controller
 * 
 * <AUTHOR>
 * @date 2024-05-15
 */
@RestController
@RequestMapping("/custom/mpuser")
public class MpUserController extends BaseController
{
    @Autowired
    private IMpUserService mpUserService;

    /**
     * 查询微信公众号用户列表
     */
    @PreAuthorize("@ss.hasPermi('custom:mpuser:list')")
    @GetMapping("/list")
    public TableDataInfo list(MpUser mpUser)
    {
        startPage();
        List<MpUser> list = mpUserService.selectMpUserList(mpUser);
        return getDataTable(list);
    }

    /**
     * 导出微信公众号用户列表
     */
    @PreAuthorize("@ss.hasPermi('custom:mpuser:export')")
    @Log(title = "微信公众号用户", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, MpUser mpUser)
    {
        List<MpUser> list = mpUserService.selectMpUserList(mpUser);
        // 根据积分倒序排序
        list.sort((a, b) -> b.getIntegral().compareTo(a.getIntegral()));
        ExcelUtil<MpUser> util = new ExcelUtil<MpUser>(MpUser.class);
        // 设置不导出的字段
        util.hideColumn("gender","avatar","status","phone");
        util.exportExcel(response, list, "微信公众号用户数据");
    }

    /**
     * 获取微信公众号用户详细信息
     */
    @PreAuthorize("@ss.hasPermi('custom:mpuser:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return success(mpUserService.selectMpUserById(id));
    }

    /**
     * 新增微信公众号用户
     */
    @PreAuthorize("@ss.hasPermi('custom:mpuser:add')")
    @Log(title = "微信公众号用户", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody MpUser mpUser)
    {
        return toAjax(mpUserService.insertMpUser(mpUser));
    }

    /**
     * 修改微信公众号用户
     */
    @PreAuthorize("@ss.hasPermi('custom:mpuser:edit')")
    @Log(title = "微信公众号用户", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody MpUser mpUser)
    {
        return toAjax(mpUserService.updateMpUser(mpUser));
    }

    /**
     * 删除微信公众号用户
     */
    @PreAuthorize("@ss.hasPermi('custom:mpuser:remove')")
    @Log(title = "微信公众号用户", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        return toAjax(mpUserService.deleteMpUserByIds(ids));
    }
}
