package com.ruoyi.custom.service.impl;

import cn.hutool.core.date.DateField;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.RandomUtil;
import com.alibaba.fastjson2.JSONArray;
import com.alibaba.fastjson2.JSONObject;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.utils.DateUtils;
import com.ruoyi.custom.domain.Article;
import com.ruoyi.custom.domain.IntegralRecord;
import com.ruoyi.custom.domain.MpUser;
import com.ruoyi.custom.mapper.ArticleMapper;
import com.ruoyi.custom.mapper.IntegralRecordMapper;
import com.ruoyi.custom.mapper.MpUserMapper;
import com.ruoyi.system.service.ISysConfigService;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.apache.http.conn.ssl.NoopHostnameVerifier;
import org.apache.http.conn.ssl.SSLConnectionSocketFactory;
import org.apache.http.conn.ssl.TrustStrategy;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClientBuilder;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.ssl.SSLContexts;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.ResponseEntity;
import org.springframework.http.client.HttpComponentsClientHttpRequestFactory;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;
import org.springframework.util.LinkedMultiValueMap;
import org.springframework.util.MultiValueMap;
import org.springframework.util.ObjectUtils;
import org.springframework.web.client.RestTemplate;

import javax.annotation.Resource;
import javax.net.ssl.SSLContext;
import java.util.Arrays;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

/**
 * @Author: y
 * @CreateDate: 2024/7/2 10:36
 * @Version: 1.0
 * @Description:
 */
@Slf4j
@Component
public class ScheduledTasks {

    @Resource
    private MpUserMapper mpUserMapper;

    @Resource
    private ArticleMapper articleMapper;

    @Resource
    private IntegralRecordMapper integralRecordMapper;

    @Autowired
    private ISysConfigService sysConfigService;

    // 每隔3个小时执行一次
    @Scheduled(fixedRate = 3 * 60 * 60 * 1000)
    public void performTask() {
        DateTime now = DateUtil.date(); // 获取当前时间
        // 获取今天早上八点
        DateTime todayMorningEight = DateUtil.beginOfDay(DateUtil.date()).offset(DateField.HOUR_OF_DAY, 8);
        // 获取今天晚上十点
        DateTime todayNightTen = DateUtil.beginOfDay(DateUtil.date()).offset(DateField.HOUR_OF_DAY, 22);
        if (!DateUtil.isIn(now, todayMorningEight, todayNightTen)) {
            return;
        }

        //t、y、y、m、j
        List<String> openIdList = Arrays.asList("obNbasmgV-tDGGnYTefPKBcsAGUs", "obNbassi_BQ5MerjvffiQvGMdKcw", "obNbasgIZOsYHTOFp3ixsCs3dSSQ","obNbaso8tdwaJzOc_gyo-Kjf1t4Y","obNbasrO12goRgGQFbLuGLcOgj5Y");

        //每日获取积分上限数量
        String dailyMaximumPoints = sysConfigService.selectConfigByKey("custom.article.daily.maximum.points");
        // 查询阅读获得积分数量
        String integralNumber = sysConfigService.selectConfigByKey("custom.article.read.integral");

        for (String openid : openIdList) {
            MpUser mpUser = mpUserMapper.selectMpUserByOpenid(openid);
            List<Article> noReadArticleList = articleMapper.selectArticleListByDotLike(openid);
            for (Article article : noReadArticleList) {
                //判断是否超出每日获得上限
                //今日已获得积分
                int curNumber = integralRecordMapper.selectDailyMaximumPointsByOpenidAndCurDate(openid);
                if ((curNumber + Integer.parseInt(integralNumber)) > Integer.parseInt(dailyMaximumPoints)) {
                    break;
                }
                int number = integralRecordMapper.selectReadNumberByArticleId(article.getId());
                if (number < 20) {
                    continue;
                }
                // 记录积分变化
                IntegralRecord integralRecord = new IntegralRecord();
                integralRecord.setOpenid(openid);
                integralRecord.setArticleId(article.getId());
                integralRecord.setIntegralNumber(Long.parseLong(integralNumber));
                integralRecord.setType(0L);
                integralRecord.setOperationTime(DateUtils.getNowDate());
                integralRecord.setRemark("阅读文章+" + integralNumber + "积分");
                int i1 = integralRecordMapper.insertIntegralRecord(integralRecord);
                log.info("【积分获取】获取详情：{}", integralRecord.toString());
                mpUser.setIntegral(mpUser.getIntegral() + Long.parseLong(integralNumber));
                mpUser.setLastTime(DateUtils.getNowDate());
                int i2 = mpUserMapper.updateMpUser(mpUser);
                try {
                    Thread.sleep(RandomUtil.randomInt(8, 16) * 1000L);
                } catch (InterruptedException e) {
                    throw new RuntimeException(e);
                }
                if (i1 < 0 || i2 < 0) {
                    throw new RuntimeException("积分获取失败");
                }
            }
        }


    }

    private static boolean flag = true;

    private static boolean result = true;

    @SneakyThrows
//    @Scheduled(fixedRate = 5000)
    private void queryScore() {
        if (!result || !flag) {
            return;
        }
        RestTemplate restTemplate = new RestTemplate(generateHttpRequestFactory());
        HttpHeaders headers = new HttpHeaders();
        headers.set("Cookie", "ZhuoFanRuanKaoUserRegID=100002211231111938934058; UUID=2411050913291907962450465; acw_tc=2f6a1fc617313099875214917ed0512c69bc47e1b829fc6493aff77e53830b; PHPSESSID=baref2k35cuuiq2ino549l6dk7; SERVERID=f7154803a54565d8f743b36388c92cfa|1731310070|1731309987");
        headers.set("Content-Length", "35");
        headers.set("Host", "bm.ruankao.org.cn");
        headers.set("Content-Type", "application/x-www-form-urlencoded; charset=UTF-8");
        headers.set("Sec-Ch-Ua", "Not.A/Brand;v=8, Chromium;v=114, Google Chrome;v=114");
        headers.set("User-Agent", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36");
        MultiValueMap<String, Object> map = new LinkedMultiValueMap<>();
        map.add("ExamTaskID", "2407120908133912062472686");
        //包装到HttpEntity中
        HttpEntity<MultiValueMap<String, Object>> request1 = new HttpEntity<>(map, headers);
        //postForEntity  -》 直接传递map参数
        ResponseEntity<String> responseEntity = restTemplate.postForEntity("https://bm.ruankao.org.cn/my/myscore/getInfo", request1, String.class);
        JSONObject jsonObject = JSONObject.parseObject(responseEntity.getBody());
        if (ObjectUtils.isEmpty(jsonObject)) {
            if (flag) {
                restTemplate.getForObject("https://api.day.app/Zh9ef6VjX3FUM6dB4oHof/成绩查询/token已过期", String.class);
                flag = false;
            }
            return;
        }
        if (!jsonObject.getString("msg").equals("ok")) {
            restTemplate.getForObject("https://api.day.app/Zh9ef6VjX3FUM6dB4oHof/成绩查询/" + jsonObject.getString("msg"), String.class);
            return;
        }
        JSONArray parse = JSONArray.parse(jsonObject.getString("data"));
        JSONObject parseObject = JSONObject.parseObject(parse.get(0).toString());
        JSONArray parse1 = JSONArray.parse(parseObject.getString("Score"));
        StringBuilder sb = new StringBuilder();
        for (Object o : parse1) {
            JSONObject jsonObject2 = JSONObject.parseObject(o.toString());
            String title = jsonObject2.getString("SubjectName");
            String score = jsonObject2.getString("Score");
            sb.append(title).append(":").append(score).append("\n");
        }
        if (result) {
            restTemplate.getForObject("https://api.day.app/Zh9ef6VjX3FUM6dB4oHof/成绩查询/" + sb.toString(), String.class);
            result = false;
        }
    }

    private static HttpComponentsClientHttpRequestFactory generateHttpRequestFactory() throws Exception {
        TrustStrategy acceptingTrustStrategy = ((x509Certificates, authType) -> true);
        SSLContext sslContext = SSLContexts.custom().loadTrustMaterial(null, acceptingTrustStrategy).build();
        SSLConnectionSocketFactory connectionSocketFactory = new SSLConnectionSocketFactory(sslContext, new NoopHostnameVerifier());
        HttpClientBuilder httpClientBuilder = HttpClients.custom();
        httpClientBuilder.setSSLSocketFactory(connectionSocketFactory);
        CloseableHttpClient httpClient = httpClientBuilder.build();
        HttpComponentsClientHttpRequestFactory factory = new HttpComponentsClientHttpRequestFactory();
        factory.setHttpClient(httpClient);
        return factory;
    }

}
