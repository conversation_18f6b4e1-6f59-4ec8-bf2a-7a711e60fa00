{"remainingRequest": "D:\\IDEA_PROJECT\\GongHuiReadIntegral\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\IDEA_PROJECT\\GongHuiReadIntegral\\ruoyi-ui\\src\\views\\custom\\goods\\index.vue?vue&type=style&index=0&id=c00ebd56&scoped=true&lang=css", "dependencies": [{"path": "D:\\IDEA_PROJECT\\GongHuiReadIntegral\\ruoyi-ui\\src\\views\\custom\\goods\\index.vue", "mtime": 1742346337609}, {"path": "D:\\IDEA_PROJECT\\GongHuiReadIntegral\\ruoyi-ui\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\IDEA_PROJECT\\GongHuiReadIntegral\\ruoyi-ui\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 499162500000}, {"path": "D:\\IDEA_PROJECT\\GongHuiReadIntegral\\ruoyi-ui\\node_modules\\postcss-loader\\src\\index.js", "mtime": 499162500000}, {"path": "D:\\IDEA_PROJECT\\GongHuiReadIntegral\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\IDEA_PROJECT\\GongHuiReadIntegral\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:CgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgouYXV0by1zaGVsZi1jYXJkIHsKICBtYXJnaW46IDEwcHggMDsKICBiYWNrZ3JvdW5kLWNvbG9yOiAjZmFmYWZhOwp9CgouYXV0by1zaGVsZi1jYXJkIDpkZWVwKC5lbC1jYXJkX19ib2R5KSB7CiAgcGFkZGluZzogMTVweCAyMHB4Owp9CgovKiDnp7vpmaTmnIDlkI7kuIDkuKrooajljZXpobnnmoTkuIvovrnot50gKi8KLmF1dG8tc2hlbGYtY2FyZCA6ZGVlcCguZWwtZm9ybS1pdGVtOmxhc3QtY2hpbGQpIHsKICBtYXJnaW4tYm90dG9tOiAwOwp9Cg=="}, {"version": 3, "sources": ["index.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA+dA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA", "file": "index.vue", "sourceRoot": "src/views/custom/goods", "sourcesContent": ["<template>\n  <div class=\"app-container\">\n    <el-form :model=\"queryParams\" ref=\"queryForm\" size=\"small\" :inline=\"true\" v-show=\"showSearch\" label-width=\"70px\">\n      <el-form-item label=\"商品标题\" prop=\"title\">\n        <el-input\n          v-model=\"queryParams.title\"\n          placeholder=\"请输入商品标题\"\n          clearable\n          @keyup.enter.native=\"handleQuery\"\n        />\n      </el-form-item>\n      <el-form-item>\n        <el-button type=\"primary\" icon=\"el-icon-search\" size=\"mini\" @click=\"handleQuery\">搜索</el-button>\n        <el-button icon=\"el-icon-refresh\" size=\"mini\" @click=\"resetQuery\">重置</el-button>\n      </el-form-item>\n    </el-form>\n\n    <el-row :gutter=\"10\" class=\"mb8\">\n      <el-col :span=\"1.5\">\n        <el-button\n          type=\"primary\"\n          plain\n          icon=\"el-icon-plus\"\n          size=\"mini\"\n          @click=\"handleAdd\"\n          v-hasPermi=\"['custom:goods:add']\"\n        >新增</el-button>\n      </el-col>\n      <el-col :span=\"1.5\">\n        <el-button\n          type=\"success\"\n          plain\n          icon=\"el-icon-edit\"\n          size=\"mini\"\n          :disabled=\"single\"\n          @click=\"handleUpdate\"\n          v-hasPermi=\"['custom:goods:edit']\"\n        >修改</el-button>\n      </el-col>\n      <el-col :span=\"1.5\">\n        <el-button\n          type=\"danger\"\n          plain\n          icon=\"el-icon-delete\"\n          size=\"mini\"\n          :disabled=\"multiple\"\n          @click=\"handleDelete\"\n          v-hasPermi=\"['custom:goods:remove']\"\n        >删除</el-button>\n      </el-col>\n      <el-col :span=\"1.5\">\n        <el-button\n          type=\"warning\"\n          plain\n          icon=\"el-icon-download\"\n          size=\"mini\"\n          @click=\"handleExport\"\n          v-hasPermi=\"['custom:goods:export']\"\n        >导出</el-button>\n      </el-col>\n      <right-toolbar :showSearch.sync=\"showSearch\" @queryTable=\"getList\"></right-toolbar>\n    </el-row>\n\n    <el-table border v-loading=\"loading\" :data=\"goodsList\" @selection-change=\"handleSelectionChange\">\n      <el-table-column type=\"selection\" width=\"55\" align=\"center\" />\n      <el-table-column label=\"ID\" align=\"center\" prop=\"id\" width=\"80\"/>\n      <el-table-column label=\"商品标题\" header-align=\"center\" align=\"left\" prop=\"title\" width=\"280\" :show-overflow-tooltip=\"true\"/>\n      <el-table-column label=\"首图\" align=\"center\" prop=\"pic\">\n        <template slot-scope=\"scope\">\n          <div class=\"demo-image__preview\">\n            <el-image :key=\"index\" v-for=\"(item,index) in scope.row.pic.split('|')\"\n                      style=\"width: 30px; height: 30px;border-radius: 5px;\"\n                      :src=\"VUE_APP_BASE_API + item\"\n                      :preview-src-list=\"[VUE_APP_BASE_API + item]\">\n            </el-image>\n          </div>\n        </template>\n      </el-table-column>\n      <el-table-column label=\"轮播图\" header-align=\"center\" align=\"left\" prop=\"pics\" width=\"200\" :show-overflow-tooltip=\"true\">\n        <template slot-scope=\"scope\">\n          <div class=\"demo-image__preview\">\n            <el-image :key=\"index\" v-for=\"(item,index) in scope.row.pics.split(',')\"\n                      style=\"width: 30px; height: 30px;border-radius: 5px;\"\n                      :src=\"VUE_APP_BASE_API + item\"\n                      :preview-src-list=\"[VUE_APP_BASE_API + item]\">\n            </el-image>\n          </div>\n        </template>\n      </el-table-column>\n      <el-table-column label=\"积分\" align=\"center\" prop=\"integral\" />\n      <el-table-column label=\"备注\" align=\"center\" prop=\"remark\" width=\"150\" :show-overflow-tooltip=\"true\"/>\n      <el-table-column label=\"状态\" align=\"center\" prop=\"status\">\n        <template slot-scope=\"scope\">\n          <el-tag v-if=\"scope.row.status === '0'\" size=\"small\" type=\"success\">正常</el-tag>\n          <el-tag v-else-if=\"scope.row.status === '1'\" size=\"small\" type=\"danger\">下架</el-tag>\n        </template>\n      </el-table-column>\n      <el-table-column label=\"库存\" align=\"center\" prop=\"stock\" />\n      <el-table-column label=\"销量\" align=\"center\" prop=\"salesVolume\" />\n      <el-table-column label=\"定时上架\" align=\"center\" prop=\"enableAutoOnShelf\">\n        <template slot-scope=\"scope\">\n          <el-tag v-if=\"scope.row.enableAutoOnShelf === 'true'\" type=\"success\">已开启</el-tag>\n          <el-tag v-else type=\"info\">未开启</el-tag>\n        </template>\n      </el-table-column>\n\n      <el-table-column label=\"操作\" align=\"center\" class-name=\"small-padding fixed-width\" fixed=\"right\" width=\"160\">\n        <template slot-scope=\"scope\">\n          <el-button\n            size=\"mini\"\n            type=\"text\"\n            icon=\"el-icon-edit\"\n            @click=\"handleUpdate(scope.row)\"\n            v-hasPermi=\"['custom:goods:edit']\"\n          >修改</el-button>\n          <el-button\n            size=\"mini\"\n            type=\"text\"\n            icon=\"el-icon-delete\"\n            @click=\"handleDelete(scope.row)\"\n            v-hasPermi=\"['custom:goods:remove']\"\n          >删除</el-button>\n        </template>\n      </el-table-column>\n    </el-table>\n\n    <pagination\n      v-show=\"total>0\"\n      :total=\"total\"\n      :page.sync=\"queryParams.pageNum\"\n      :limit.sync=\"queryParams.pageSize\"\n      @pagination=\"getList\"\n    />\n\n    <!-- 添加或修改商品对话框 -->\n    <el-dialog :title=\"title\" :visible.sync=\"open\" width=\"35%\" :close-on-click-modal=\"false\" append-to-body>\n      <el-form ref=\"form\" :model=\"form\" :rules=\"rules\" label-width=\"80px\">\n        <el-form-item label=\"商品标题\" prop=\"title\">\n          <el-input v-model=\"form.title\" placeholder=\"请输入商品标题\" />\n        </el-form-item>\n        <el-form-item label=\"首图\" prop=\"pic\">\n          <image-upload :limit=\"1\" v-model=\"form.pic\"/>\n        </el-form-item>\n        <el-form-item label=\"轮播图\" prop=\"pics\">\n          <image-upload :limit=\"5\" v-model=\"form.pics\"/>\n        </el-form-item>\n        <el-row>\n          <el-col :span=\"12\">\n            <el-form-item label=\"积分\" prop=\"integral\">\n              <el-input v-model=\"form.integral\" type=\"number\" min=\"0\" placeholder=\"请输入积分\" />\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"12\">\n            <el-form-item label=\"库存\" prop=\"stock\">\n              <el-input v-model=\"form.stock\" type=\"number\" min=\"0\" placeholder=\"请输入库存\" />\n            </el-form-item>\n          </el-col>\n        </el-row>\n        <el-form-item label=\"状态\" prop=\"status\">\n          <el-radio-group v-model=\"form.status\">\n            <el-radio\n              v-for=\"dict in dict.type.custom_goods_status\"\n              :key=\"dict.value\"\n              :label=\"dict.value\"\n            >{{dict.label}}</el-radio>\n          </el-radio-group>\n        </el-form-item>\n        <el-form-item label=\"定时上架\" prop=\"enableAutoOnShelf\">\n          <el-switch v-model=\"form.enableAutoOnShelf\" @change=\"handleAutoOnShelfChange\"/>\n        </el-form-item>\n        <el-card v-if=\"form.enableAutoOnShelf\" class=\"auto-shelf-card\" shadow=\"never\">\n          <el-form-item label=\"上架方式\" prop=\"onShelfType\">\n            <el-radio-group v-model=\"form.onShelfType\" size=\"small\">\n              <el-radio label=\"weekly\">每周</el-radio>\n              <el-radio label=\"specific\">指定时间</el-radio>\n            </el-radio-group>\n          </el-form-item>\n          <el-form-item v-if=\"form.onShelfType === 'weekly'\" label=\"上架时间\" prop=\"weeklyOnShelfTime\">\n            <el-col :span=\"11\">\n              <el-select v-model=\"form.weekDay\" placeholder=\"请选择星期\" style=\"width: 95%\">\n                <el-option label=\"星期一\" value=\"1\"/>\n                <el-option label=\"星期二\" value=\"2\"/>\n                <el-option label=\"星期三\" value=\"3\"/>\n                <el-option label=\"星期四\" value=\"4\"/>\n                <el-option label=\"星期五\" value=\"5\"/>\n                <el-option label=\"星期六\" value=\"6\"/>\n                <el-option label=\"星期日\" value=\"7\"/>\n              </el-select>\n            </el-col>\n            <el-col :span=\"2\" class=\"text-center\">\n              <span class=\"el-icon-time\"></span>\n            </el-col>\n            <el-col :span=\"11\">\n              <el-time-picker\n                v-model=\"form.weekTime\"\n                placeholder=\"选择时间\"\n                format=\"HH:mm:ss\"\n                value-format=\"HH:mm:ss\"\n                style=\"width: 95%\"\n              />\n            </el-col>\n          </el-form-item>\n          <el-form-item v-if=\"form.onShelfType === 'specific'\" label=\"上架时间\" prop=\"specificOnShelfTime\">\n            <el-date-picker\n              v-model=\"form.specificTime\"\n              type=\"datetime\"\n              placeholder=\"选择上架时间\"\n              value-format=\"yyyy-MM-dd HH:mm:ss\"\n              style=\"width: 50%\"\n            />\n          </el-form-item>\n          <el-form-item label=\"上架数量\" prop=\"onShelfQuantity\">\n            <el-input-number\n              v-model=\"form.onShelfQuantity\"\n              :min=\"1\"\n              placeholder=\"请输入上架数量\"\n              style=\"width: 200px\"\n            />\n          </el-form-item>\n        </el-card>\n        <el-form-item label=\"详情\" prop=\"detail\">\n          <editor v-model=\"form.detail\" :min-height=\"192\"/>\n        </el-form-item>\n        <el-form-item label=\"备注\" prop=\"remark\">\n          <el-input v-model=\"form.remark\" type=\"textarea\" rows=\"3\" placeholder=\"请输入备注\" />\n        </el-form-item>\n      </el-form>\n      <div slot=\"footer\" class=\"dialog-footer\">\n        <el-button type=\"primary\" @click=\"submitForm\">确 定</el-button>\n        <el-button @click=\"cancel\">取 消</el-button>\n      </div>\n    </el-dialog>\n  </div>\n</template>\n\n<script>\nimport { listGoods, getGoods, delGoods, addGoods, updateGoods } from \"@/api/custom/goods\";\n\nexport default {\n  name: \"Goods\",\n  dicts: ['custom_goods_status'],\n  data() {\n    return {\n      VUE_APP_BASE_API: process.env.VUE_APP_BASE_API,\n      // 遮罩层\n      loading: true,\n      // 选中数组\n      ids: [],\n      // 非单个禁用\n      single: true,\n      // 非多个禁用\n      multiple: true,\n      // 显示搜索条件\n      showSearch: true,\n      // 总条数\n      total: 0,\n      // 商品表格数据\n      goodsList: [],\n      // 弹出层标题\n      title: \"\",\n      // 是否显示弹出层\n      open: false,\n      // 查询参数\n      queryParams: {\n        pageNum: 1,\n        pageSize: 10,\n        title: null,\n        integral: null,\n        pic: null,\n        pics: null,\n        detail: null,\n        status: null,\n        stock: null,\n        salesVolume: null,\n      },\n      // 表单参数\n      form: {\n        id: null,\n        title: null,\n        integral: null,\n        pic: null,\n        pics: null,\n        detail: null,\n        remark: null,\n        status: \"0\",\n        stock: null,\n        salesVolume: null,\n        createTime: null,\n        updateTime: null,\n        enableAutoOnShelf: false,\n        onShelfType: 'weekly',\n        weekDay: '1',\n        weekTime: '',\n        specificTime: '',\n        onShelfQuantity: 1\n      },\n      // 表单校验\n      rules: {\n        title: [\n          { required: true, message: \"商品标题不能为空\", trigger: \"blur\" }\n        ],\n        integral: [\n          { required: true, message: \"积分不能为空\", trigger: \"blur\" }\n        ],\n        pic: [\n          { required: true, message: \"首图不能为空\", trigger: \"blur\" }\n        ],\n        pics: [\n          { required: true, message: \"轮播图不能为空\", trigger: \"blur\" }\n        ],\n        status: [\n          { required: true, message: \"状态不能为空\", trigger: \"change\" }\n        ],\n        stock: [\n          { required: true, message: \"库存不能为空\", trigger: \"blur\" }\n        ],\n        salesVolume: [\n          { required: true, message: \"销量不能为空\", trigger: \"blur\" }\n        ],\n        onShelfQuantity: [\n          { required: true, message: \"上架数量不能为空\", trigger: \"blur\" }\n        ],\n        weekDay: [\n          { required: true, message: \"请选择星期\", trigger: \"change\" }\n        ],\n        weekTime: [\n          { required: true, message: \"请选择时间\", trigger: \"change\" }\n        ],\n        specificTime: [\n          { required: true, message: \"请选择上架时间\", trigger: \"change\" }\n        ]\n      }\n    };\n  },\n  created() {\n    this.getList();\n  },\n  methods: {\n    /** 查询商品列表 */\n    getList() {\n      this.loading = true;\n      listGoods(this.queryParams).then(response => {\n        this.goodsList = response.rows;\n        this.total = response.total;\n        this.loading = false;\n      });\n    },\n    // 取消按钮\n    cancel() {\n      this.open = false;\n      this.reset();\n    },\n    // 表单重置\n    reset() {\n      this.form = {\n        id: null,\n        title: null,\n        integral: null,\n        pic: null,\n        pics: null,\n        detail: null,\n        remark: null,\n        status: \"0\",\n        stock: null,\n        salesVolume: null,\n        createTime: null,\n        updateTime: null,\n        enableAutoOnShelf: false,\n        onShelfType: 'weekly',\n        weekDay: '1',\n        weekTime: '',\n        specificTime: '',\n        onShelfQuantity: 1\n      };\n      this.resetForm(\"form\");\n    },\n    /** 搜索按钮操作 */\n    handleQuery() {\n      this.queryParams.pageNum = 1;\n      this.getList();\n    },\n    /** 重置按钮操作 */\n    resetQuery() {\n      this.resetForm(\"queryForm\");\n      this.handleQuery();\n    },\n    // 多选框选中数据\n    handleSelectionChange(selection) {\n      this.ids = selection.map(item => item.id)\n      this.single = selection.length!==1\n      this.multiple = !selection.length\n    },\n    /** 新增按钮操作 */\n    handleAdd() {\n      this.reset();\n      this.open = true;\n      this.title = \"添加商品\";\n    },\n    /** 修改按钮操作 */\n    handleUpdate(row) {\n      this.reset();\n      const id = row.id || this.ids\n      getGoods(id).then(response => {\n        this.form = response.data;\n        this.form.enableAutoOnShelf = this.form.enableAutoOnShelf === \"true\";\n        this.open = true;\n        this.title = \"修改商品\";\n      });\n    },\n    /** 提交按钮 */\n    submitForm() {\n      this.$refs[\"form\"].validate(valid => {\n        if (valid) {\n          // 创建一个新对象来保存表单数据\n          const submitData = {\n            ...this.form,\n            // 确保发送给后端的是字符串类型\n            enableAutoOnShelf: this.form.enableAutoOnShelf.toString()\n          };\n\n          // 如果自动上架关闭，相关字段设置为 null\n          if (!this.form.enableAutoOnShelf) {\n            submitData.onShelfType = null;\n            submitData.weekDay = null;\n            submitData.weekTime = null;\n            submitData.specificTime = null;\n            submitData.onShelfQuantity = null;\n          }\n\n          if (this.form.id != null) {\n            updateGoods(submitData).then(response => {\n              this.$modal.msgSuccess(\"修改成功\");\n              this.open = false;\n              this.getList();\n            });\n          } else {\n            addGoods(submitData).then(response => {\n              this.$modal.msgSuccess(\"新增成功\");\n              this.open = false;\n              this.getList();\n            });\n          }\n        }\n      });\n    },\n    /** 删除按钮操作 */\n    handleDelete(row) {\n      const ids = row.id || this.ids;\n      this.$modal.confirm('是否确认删除商品编号为\"' + ids + '\"的数据项？').then(function() {\n        return delGoods(ids);\n      }).then(() => {\n        this.getList();\n        this.$modal.msgSuccess(\"删除成功\");\n      }).catch(() => {});\n    },\n    /** 导出按钮操作 */\n    handleExport() {\n      this.$modal.confirm('是否确认导出所有商品数据？').then(() => {\n        this.download('custom/goods/export', {\n          ...this.queryParams\n        }, `goods_${new Date().getTime()}.xlsx`)\n      }).catch(() => {});\n    },\n    // 处理定时上架开关变化\n    handleAutoOnShelfChange(val) {\n      if (!val) {\n        // 关闭开关时，重置所有相关字段\n        this.form.onShelfType = 'weekly';\n        this.form.weekDay = '1';\n        this.form.weekTime = '';\n        this.form.specificTime = '';\n        this.form.onShelfQuantity = 1;\n      }\n    }\n  }\n};\n</script>\n\n<style scoped>\n.auto-shelf-card {\n  margin: 10px 0;\n  background-color: #fafafa;\n}\n\n.auto-shelf-card :deep(.el-card__body) {\n  padding: 15px 20px;\n}\n\n/* 移除最后一个表单项的下边距 */\n.auto-shelf-card :deep(.el-form-item:last-child) {\n  margin-bottom: 0;\n}\n</style>\n"]}]}