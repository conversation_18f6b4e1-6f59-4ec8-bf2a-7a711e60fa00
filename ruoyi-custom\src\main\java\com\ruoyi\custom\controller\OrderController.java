package com.ruoyi.custom.controller;

import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.ArrayList;
import javax.servlet.http.HttpServletResponse;

import com.alibaba.fastjson2.JSONObject;
import com.ruoyi.custom.domain.OrderExport;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.custom.domain.Order;
import com.ruoyi.custom.service.IOrderService;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.common.core.page.TableDataInfo;

/**
 * 订单Controller
 * 
 * <AUTHOR>
 * @date 2024-05-16
 */
@RestController
@RequestMapping("/custom/order")
public class OrderController extends BaseController
{
    @Autowired
    private IOrderService orderService;

    /**
     * 查询订单列表
     */
    @PreAuthorize("@ss.hasPermi('custom:order:list')")
    @GetMapping("/list")
    public TableDataInfo list(Order order)
    {
        startPage();
        List<Order> list = orderService.selectOrderList(order);
        return getDataTable(list);
    }

    /**
     * 导出订单列表
     */
    @PreAuthorize("@ss.hasPermi('custom:order:export')")
    @Log(title = "订单", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, String ids)
    {
        List<String> idList = Arrays.asList(ids.split(","));
        List<JSONObject> list =  orderService.selectOrderByIds(idList);
        List<OrderExport> exportList = new ArrayList<>();
        
        for (JSONObject obj : list) {
            OrderExport export = new OrderExport();
            export.setNickname(obj.getString("nickname"));
            export.setTitle(obj.getString("title"));
            export.setRedemptionCode(obj.getString("redemption_code"));
            // 以下三个字段设置为空
            export.setName("");
            export.setPhone("");
            export.setRewardDate(null);
            exportList.add(export);
        }
        
        ExcelUtil<OrderExport> util = new ExcelUtil<OrderExport>(OrderExport.class);
        util.exportExcel(response, exportList, "兑换记录");
    }

    /**
     * 获取订单详细信息
     */
    @PreAuthorize("@ss.hasPermi('custom:order:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return success(orderService.selectOrderById(id));
    }

    /**
     * 新增订单
     */
    @PreAuthorize("@ss.hasPermi('custom:order:add')")
    @Log(title = "订单", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody Order order)
    {
        return toAjax(orderService.insertOrder(order));
    }

    /**
     * 修改订单
     */
    @PreAuthorize("@ss.hasPermi('custom:order:edit')")
    @Log(title = "订单", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody Order order)
    {
        return toAjax(orderService.updateOrder(order));
    }


    @PreAuthorize("@ss.hasPermi('custom:order:edit')")
    @Log(title = "订单状态修改", businessType = BusinessType.UPDATE)
    @PutMapping("/orderStatus/{oId}")
    public AjaxResult orderStatus(@PathVariable("oId") Long oId)
    {
        return orderService.orderStatus(oId);
    }

    /**
     * 删除订单
     */
    @PreAuthorize("@ss.hasPermi('custom:order:remove')")
    @Log(title = "订单", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        return toAjax(orderService.deleteOrderByIds(ids));
    }
}
