package com.ruoyi.custom.domain;

import java.util.Date;
import java.util.List;

import com.fasterxml.jackson.annotation.JsonFormat;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;
import org.springframework.data.annotation.Transient;

/**
 * 订单对象 tbl_order
 * 
 * <AUTHOR>
 * @date 2024-05-16
 */
public class Order extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /**  */
    private Long id;

    /** 兑换用户openid */
    @Excel(name = "兑换用户openid")
    private String openid;

    @Transient
    private List<String> openidList;

    @Transient
    private String nickname;

    /** 商品id */
    @Excel(name = "商品id")
    private Long goodsId;

    @Transient
    private String goodsName;

    /** 兑换时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "兑换时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date exchangeTime;

    /** 兑换状态0未兑换，1已兑换 */
    @Excel(name = "兑换状态0未兑换，1已兑换")
    private String status;

    /** 兑换积分 */
    @Excel(name = "兑换积分")
    private Long integralNumber;

    /** 兑换码 */
    @Excel(name = "兑换码")
    private String redemptionCode;

    @Transient
    private String address;

    public String getAddress() {
        return address;
    }

    public void setAddress(String address) {
        this.address = address;
    }

    public void setId(Long id)
    {
        this.id = id;
    }

    public List<String> getOpenidList() {
        return openidList;
    }

    public void setOpenidList(List<String> openidList) {
        this.openidList = openidList;
    }

    public Long getId()
    {
        return id;
    }
    public void setOpenid(String openid) 
    {
        this.openid = openid;
    }

    public String getNickname() {
        return nickname;
    }

    public void setNickname(String nickname) {
        this.nickname = nickname;
    }

    public String getOpenid()
    {
        return openid;
    }
    public void setGoodsId(Long goodsId) 
    {
        this.goodsId = goodsId;
    }

    public Long getGoodsId() 
    {
        return goodsId;
    }
    public void setExchangeTime(Date exchangeTime) 
    {
        this.exchangeTime = exchangeTime;
    }

    public Date getExchangeTime() 
    {
        return exchangeTime;
    }
    public void setStatus(String status) 
    {
        this.status = status;
    }

    public String getStatus() 
    {
        return status;
    }
    public void setIntegralNumber(Long integralNumber) 
    {
        this.integralNumber = integralNumber;
    }

    public Long getIntegralNumber() 
    {
        return integralNumber;
    }
    public void setRedemptionCode(String redemptionCode) 
    {
        this.redemptionCode = redemptionCode;
    }

    public String getRedemptionCode() 
    {
        return redemptionCode;
    }

    public String getGoodsName() {
        return goodsName;
    }

    public void setGoodsName(String goodsName) {
        this.goodsName = goodsName;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("id", getId())
            .append("openid", getOpenid())
            .append("goodsId", getGoodsId())
            .append("exchangeTime", getExchangeTime())
            .append("status", getStatus())
            .append("integralNumber", getIntegralNumber())
            .append("redemptionCode", getRedemptionCode())
            .append("createTime", getCreateTime())
            .append("updateTime", getUpdateTime())
            .toString();
    }
}
