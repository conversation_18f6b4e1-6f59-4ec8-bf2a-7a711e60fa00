package com.ruoyi.custom.service;

import java.time.LocalDateTime;
import java.util.List;

import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.custom.domain.Goods;

/**
 * 商品Service接口
 * 
 * <AUTHOR>
 * @date 2024-05-16
 */
public interface IGoodsService 
{
    /**
     * 查询商品
     * 
     * @param id 商品主键
     * @return 商品
     */
    public Goods selectGoodsById(Long id);

    /**
     * 查询商品列表
     * 
     * @param goods 商品
     * @return 商品集合
     */
    public List<Goods> selectGoodsList(Goods goods);

    /**
     * 新增商品
     * 
     * @param goods 商品
     * @return 结果
     */
    public int insertGoods(Goods goods);

    /**
     * 修改商品
     * 
     * @param goods 商品
     * @return 结果
     */
    public int updateGoods(Goods goods);

    /**
     * 批量删除商品
     * 
     * @param ids 需要删除的商品主键集合
     * @return 结果
     */
    public int deleteGoodsByIds(Long[] ids);

    /**
     * 删除商品信息
     * 
     * @param id 商品主键
     * @return 结果
     */
    public int deleteGoodsById(Long id);

    AjaxResult goodsList();

    int selectGoodsAllCount();

    /**
     * 查询需要每周定时上架的商品
     */
    public List<Goods> selectWeeklyOnShelfGoods(int weekDay, String currentTime);

    /**
     * 查询需要在指定时间上架的商品
     */
    public List<Goods> selectSpecificOnShelfGoods(LocalDateTime currentTime);

    /**
     * 原子性增加库存，用于自动上架
     * @param goodsId 商品ID
     * @param quantity 增加的库存数量
     * @return 影响的行数
     */
    int increaseStockForAutoOnShelf(Long goodsId, Long quantity);
}
