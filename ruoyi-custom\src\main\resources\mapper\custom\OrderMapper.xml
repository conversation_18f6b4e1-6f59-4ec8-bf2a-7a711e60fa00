<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.custom.mapper.OrderMapper">
    
    <resultMap type="Order" id="OrderResult">
        <result property="id"    column="id"    />
        <result property="openid"    column="openid"    />
        <result property="goodsId"    column="goods_id"    />
        <result property="exchangeTime"    column="exchange_time"    />
        <result property="status"    column="status"    />
        <result property="integralNumber"    column="integral_number"    />
        <result property="redemptionCode"    column="redemption_code"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateTime"    column="update_time"    />
    </resultMap>

    <sql id="selectOrderVo">
        select id, openid, goods_id, exchange_time, status, integral_number, redemption_code, create_time, update_time from tbl_order
    </sql>

    <select id="selectOrderList" parameterType="Order" resultMap="OrderResult">
        <include refid="selectOrderVo"/>
        <where>
            <if test="openidList != null  and openidList != ''">
                and openid in
                <foreach collection="openidList" item="item" index="index"
                         separator="," open="(" close=")">
                    #{item}
                </foreach>
            </if>
            <if test="goodsId != null "> and goods_id = #{goodsId}</if>
            <if test="exchangeTime != null "> and date(exchange_time) = #{exchangeTime}</if>
            <if test="status != null  and status != ''"> and status = #{status}</if>
            <if test="integralNumber != null "> and integral_number = #{integralNumber}</if>
            <if test="redemptionCode != null  and redemptionCode != ''"> and redemption_code like concat('%', #{redemptionCode}, '%')</if>
        </where>
        order by id desc
    </select>

    <select id="selectOrderListByOpenid" parameterType="String" resultMap="OrderResult">
        <include refid="selectOrderVo"/>
        where openid = #{openid}
        order by id desc
    </select>
    <select id="selectOrderById" parameterType="Long" resultMap="OrderResult">
        <include refid="selectOrderVo"/>
        where id = #{id}
    </select>
    <select id="selectOrderAllCount" resultType="java.lang.Integer">
        SELECT COUNT(*) AS number FROM `tbl_order`
    </select>
    <select id="numberOfExchanges" parameterType="List" resultType="com.alibaba.fastjson2.JSONObject">
        SELECT
            COUNT(*) AS number,
            DATE( create_time ) AS ct
        FROM
            `tbl_order`
        WHERE
            DATE( create_time ) IN
            <foreach item="item" collection="list" open="(" separator="," close=")">
                #{item}
            </foreach>
        GROUP BY
            ct
        ORDER BY
            ct
    </select>
    <select id="numberOfSales" parameterType="List" resultType="com.alibaba.fastjson2.JSONObject">
        SELECT
            COUNT(*) AS number,
            DATE( exchange_time ) AS ct
        FROM
            `tbl_order`
        WHERE `status` = '1' AND
            DATE( exchange_time ) IN
            <foreach item="item" collection="list" open="(" separator="," close=")">
                #{item}
            </foreach>
        GROUP BY
            ct
        ORDER BY
            ct
    </select>
    <select id="selectOrderByIds"  parameterType="List" resultType="com.alibaba.fastjson2.JSONObject">
        SELECT
        COALESCE(u.nickname, '无昵称') AS nickname,
        COALESCE(g.title, '无商品标题') AS title,
        o.redemption_code
        FROM
        tbl_order AS o
        LEFT JOIN tbl_mp_user u ON o.openid = u.openid
        LEFT JOIN tbl_goods g ON o.goods_id = g.id
        WHERE
        o.id IN
        <foreach item="item" collection="list" open="(" separator="," close=")">
            #{item}
        </foreach>
        ORDER BY
        o.id DESC
    </select>

    <insert id="insertOrder" parameterType="Order" useGeneratedKeys="true" keyProperty="id">
        insert into tbl_order
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="openid != null and openid != ''">openid,</if>
            <if test="goodsId != null">goods_id,</if>
            <if test="exchangeTime != null">exchange_time,</if>
            <if test="status != null and status != ''">status,</if>
            <if test="integralNumber != null">integral_number,</if>
            <if test="redemptionCode != null and redemptionCode != ''">redemption_code,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateTime != null">update_time,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="openid != null and openid != ''">#{openid},</if>
            <if test="goodsId != null">#{goodsId},</if>
            <if test="exchangeTime != null">#{exchangeTime},</if>
            <if test="status != null and status != ''">#{status},</if>
            <if test="integralNumber != null">#{integralNumber},</if>
            <if test="redemptionCode != null and redemptionCode != ''">#{redemptionCode},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateTime != null">#{updateTime},</if>
         </trim>
    </insert>

    <update id="updateOrder" parameterType="Order">
        update tbl_order
        <trim prefix="SET" suffixOverrides=",">
            <if test="openid != null and openid != ''">openid = #{openid},</if>
            <if test="goodsId != null">goods_id = #{goodsId},</if>
            <if test="exchangeTime != null">exchange_time = #{exchangeTime},</if>
            <if test="status != null and status != ''">status = #{status},</if>
            <if test="integralNumber != null">integral_number = #{integralNumber},</if>
            <if test="redemptionCode != null and redemptionCode != ''">redemption_code = #{redemptionCode},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteOrderById" parameterType="Long">
        delete from tbl_order where id = #{id}
    </delete>

    <delete id="deleteOrderByIds" parameterType="String">
        delete from tbl_order where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>