package com.ruoyi.custom.service;

import java.util.List;
import com.ruoyi.custom.domain.ExchangeRecord;

/**
 * 兑换记录Service接口
 * 
 * <AUTHOR>
 * @date 2025-01-02
 */
public interface IExchangeRecordService 
{
    /**
     * 查询兑换记录
     * 
     * @param id 兑换记录主键
     * @return 兑换记录
     */
    public ExchangeRecord selectExchangeRecordById(Long id);

    /**
     * 查询兑换记录列表
     * 
     * @param exchangeRecord 兑换记录
     * @return 兑换记录集合
     */
    public List<ExchangeRecord> selectExchangeRecordList(ExchangeRecord exchangeRecord);

    /**
     * 新增兑换记录
     * 
     * @param exchangeRecord 兑换记录
     * @return 结果
     */
    public int insertExchangeRecord(ExchangeRecord exchangeRecord);

    /**
     * 修改兑换记录
     * 
     * @param exchangeRecord 兑换记录
     * @return 结果
     */
    public int updateExchangeRecord(ExchangeRecord exchangeRecord);

    /**
     * 批量删除兑换记录
     * 
     * @param ids 需要删除的兑换记录主键集合
     * @return 结果
     */
    public int deleteExchangeRecordByIds(Long[] ids);

    /**
     * 删除兑换记录信息
     * 
     * @param id 兑换记录主键
     * @return 结果
     */
    public int deleteExchangeRecordById(Long id);

    int selectExchangeRecordByUidAndGoodsId(String openid,Long goodsId);

}
