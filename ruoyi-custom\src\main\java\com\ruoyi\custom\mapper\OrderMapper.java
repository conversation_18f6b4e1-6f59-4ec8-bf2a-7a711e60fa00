package com.ruoyi.custom.mapper;

import java.util.List;

import com.alibaba.fastjson2.JSONObject;
import com.ruoyi.custom.domain.Order;
import org.apache.ibatis.annotations.Param;

/**
 * 订单Mapper接口
 * 
 * <AUTHOR>
 * @date 2024-05-16
 */
public interface OrderMapper 
{
    /**
     * 查询订单
     * 
     * @param id 订单主键
     * @return 订单
     */
    public Order selectOrderById(Long id);

    /**
     * 查询订单列表
     * 
     * @param order 订单
     * @return 订单集合
     */
    public List<Order> selectOrderList(Order order);

    /**
     * 新增订单
     * 
     * @param order 订单
     * @return 结果
     */
    public int insertOrder(Order order);

    /**
     * 修改订单
     * 
     * @param order 订单
     * @return 结果
     */
    public int updateOrder(Order order);

    /**
     * 删除订单
     * 
     * @param id 订单主键
     * @return 结果
     */
    public int deleteOrderById(Long id);

    /**
     * 批量删除订单
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteOrderByIds(Long[] ids);

    List<Order> selectOrderListByOpenid(String openid);

    int selectOrderAllCount();

    List<JSONObject> numberOfExchanges(@Param("list") List<String> dateRangeList);

    List<JSONObject> numberOfSales(@Param("list") List<String> dateRangeList);

    List<JSONObject> selectOrderByIds(@Param("list") List<String> ids);
}
