package com.ruoyi.custom.controller;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateField;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.io.IoUtil;
import cn.hutool.http.server.HttpServerResponse;
import cn.hutool.poi.excel.ExcelUtil;
import cn.hutool.poi.excel.ExcelWriter;
import com.alibaba.fastjson2.JSONObject;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.custom.service.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.OutputStream;
import java.io.PrintWriter;
import java.util.*;
import java.util.stream.Collectors;

/**
 * @Author: y
 * @CreateDate: 2024/5/17 8:41
 * @Version: 1.0
 * @Description:
 */
@RestController
@RequestMapping("/index")
public class IndexController {

    @Autowired
    private IMpUserService mpUserService;

    @Autowired
    private IArticleService articleService;

    @Autowired
    private IGoodsService goodsService;

    @Autowired
    private IIntegralRecordService integralRecordService;

    @Autowired
    private IOrderService orderService;

    /**
     * 获取数据统计
     */
    @PostMapping("/statistics")
    public AjaxResult statistics() {

        int mpUserCount = mpUserService.selectMpUserAllCount();
        int articleCount = articleService.selectArticleAllCount();
        int integralCount = mpUserService.selectMpUserIntegralAllCount();
        int orderCount = orderService.selectOrderAllCount();

        JSONObject jsonObject = new JSONObject();
        jsonObject.put("mpUserCount", mpUserCount);
        jsonObject.put("articleCount", articleCount);
        jsonObject.put("integralCount", integralCount);
        jsonObject.put("orderCount", orderCount);

        return AjaxResult.success(jsonObject);
    }

    /**
     * 查询一周积分获取数量
     */
    @PostMapping("/oneWeekPoints/{dateRange}")
    public AjaxResult oneWeekPoints(@PathVariable("dateRange") String dateRange) {
        return integralRecordService.oneWeekPoints(dateRange);
    }

    /**
     * 使用hutool导出数据
     */
    @PostMapping("/exportData/{dateRange}")
    public void exportData(@PathVariable("dateRange") String dateRange, HttpServletResponse response) throws IOException {
        ExcelWriter writer = ExcelUtil.getWriter(true);
        writer.setColumnWidth(0, 15);
        writer.setColumnWidth(3, 20);

        List<DateTime> dateTimeCollect = Arrays.stream(dateRange.split(","))
                .map(DateUtil::parse)
                .collect(Collectors.toList());

        List<String> dateRangeList = DateUtil.rangeToList(dateTimeCollect.get(0), dateTimeCollect.get(1), DateField.DAY_OF_YEAR)
                .stream()
                .map(item -> DateUtil.format(item, "yyyy-MM-dd"))
                .collect(Collectors.toList());

        // 获取统计数据
        List<JSONObject> numberOfViewers = integralRecordService.numberOfViewers(dateRangeList);
        List<JSONObject> numberOfPointsObtained = integralRecordService.numberOfPointsObtained(dateRangeList);
        List<JSONObject> numberOfExchanges = orderService.numberOfExchanges(dateRangeList);
        List<JSONObject> numberOfSales = orderService.numberOfSales(dateRangeList);

        // 构建数据列表
        List<Map<String, Object>> rows = dateRangeList.stream()
                .map(date -> {
                    Map<String, Object> row = new LinkedHashMap<>();
                    row.put("日期", date);
                    row.put("浏览次数", 0);
                    row.put("参与人数", 0);
                    row.put("获得积分数量", 0);
                    row.put("兑换量", 0);
                    row.put("核销量", 0);
                    return row;
                })
                .collect(Collectors.toList());

        // 填充统计数据
        for (JSONObject jsonObject : numberOfViewers) {
            String otDate = DateUtil.format(DateUtil.parse(jsonObject.getString("ot")), "yyyy-MM-dd");
            rows.stream()
                    .filter(row -> Objects.equals(row.get("日期"), otDate))
                    .forEach(row -> row.put("参与人数", jsonObject.getInteger("number")));
        }

        for (JSONObject jsonObject : numberOfPointsObtained) {
            String otDate = DateUtil.format(DateUtil.parse(jsonObject.getString("ot")), "yyyy-MM-dd");
            rows.stream()
                    .filter(row -> Objects.equals(row.get("日期"), otDate))
                    .forEach(row -> {
                        row.put("获得积分数量", jsonObject.getInteger("number"));
                        row.put("浏览次数", jsonObject.getInteger("number"));
                    });
        }

        for (JSONObject jsonObject : numberOfExchanges) {
            String ctDate = DateUtil.format(DateUtil.parse(jsonObject.getString("ct")), "yyyy-MM-dd");
            rows.stream()
                    .filter(row -> Objects.equals(row.get("日期"), ctDate))
                    .forEach(row -> row.put("兑换量", jsonObject.getInteger("number")));
        }

        for (JSONObject jsonObject : numberOfSales) {
            String ctDate = DateUtil.format(DateUtil.parse(jsonObject.getString("ct")), "yyyy-MM-dd");
            rows.stream()
                    .filter(row -> Objects.equals(row.get("日期"), ctDate))
                    .forEach(row -> row.put("核销量", jsonObject.getInteger("number")));
        }

        // 写入数据到Excel
        writer.write(rows, true);

        // 设置响应头并将数据输出到响应流
        response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet;charset=utf-8");
        response.setHeader("Content-Disposition", "attachment;filename=test.xlsx");
        ServletOutputStream out = response.getOutputStream();
        writer.flush(out, true);
        writer.close();
        IoUtil.close(out);

    }

}
