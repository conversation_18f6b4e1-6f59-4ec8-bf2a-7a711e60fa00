package com.ruoyi.custom.service;

import java.util.List;

import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.custom.domain.MpUser;
import me.chanjar.weixin.common.bean.oauth2.WxOAuth2AccessToken;

/**
 * 微信公众号用户Service接口
 * 
 * <AUTHOR>
 * @date 2024-05-15
 */
public interface IMpUserService 
{
    /**
     * 查询微信公众号用户
     * 
     * @param id 微信公众号用户主键
     * @return 微信公众号用户
     */
    public MpUser selectMpUserById(Long id);

    /**
     * 查询微信公众号用户列表
     * 
     * @param mpUser 微信公众号用户
     * @return 微信公众号用户集合
     */
    public List<MpUser> selectMpUserList(MpUser mpUser);

    /**
     * 新增微信公众号用户
     * 
     * @param mpUser 微信公众号用户
     * @return 结果
     */
    public int insertMpUser(MpUser mpUser);

    /**
     * 修改微信公众号用户
     * 
     * @param mpUser 微信公众号用户
     * @return 结果
     */
    public int updateMpUser(MpUser mpUser);

    /**
     * 批量删除微信公众号用户
     * 
     * @param ids 需要删除的微信公众号用户主键集合
     * @return 结果
     */
    public int deleteMpUserByIds(Long[] ids);

    /**
     * 删除微信公众号用户信息
     * 
     * @param id 微信公众号用户主键
     * @return 结果
     */
    public int deleteMpUserById(Long id);

    String checkMpUserExist(WxOAuth2AccessToken wxOAuth2AccessToken);

    AjaxResult mpUserInfo(String token);

    AjaxResult updUserInfo(String token, String nickname, String avatar);

    int selectMpUserAllCount();

    int selectMpUserIntegralAllCount();

}
