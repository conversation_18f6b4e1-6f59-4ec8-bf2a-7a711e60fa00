{"remainingRequest": "D:\\IDEA_PROJECT\\GongHuiReadIntegral\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\IDEA_PROJECT\\GongHuiReadIntegral\\ruoyi-ui\\src\\views\\custom\\goods\\index.vue?vue&type=template&id=c00ebd56&scoped=true", "dependencies": [{"path": "D:\\IDEA_PROJECT\\GongHuiReadIntegral\\ruoyi-ui\\src\\views\\custom\\goods\\index.vue", "mtime": 1742346337609}, {"path": "D:\\IDEA_PROJECT\\GongHuiReadIntegral\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\IDEA_PROJECT\\GongHuiReadIntegral\\ruoyi-ui\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 499162500000}, {"path": "D:\\IDEA_PROJECT\\GongHuiReadIntegral\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\IDEA_PROJECT\\GongHuiReadIntegral\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, null]}