package com.ruoyi.common.utils.jwt;

import cn.hutool.core.date.DateField;
import cn.hutool.core.date.DateTime;
import cn.hutool.json.JSONObject;
import cn.hutool.jwt.JWT;
import cn.hutool.jwt.JWTPayload;
import cn.hutool.jwt.JWTUtil;
import com.ruoyi.common.utils.StringUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import java.util.HashMap;
import java.util.Map;
/**
 * @Author: y
 * @CreateDate: 2024/5/15 15:21
 * @Version: 1.0
 * @Description:
 */

@Slf4j
@Component
public class JwtUtil {

    /**
     * 盐值很重要，不能泄漏，且每个项目都应该不一样，可以放到配置文件中
     */
    private static final String KEY = "abcdefghijklmnopqrstuxking";

    /**
     * 创建token
     * @param id
     * @param name
     * @return
     */
    public static String createToken(String id, String name) {
        DateTime now = DateTime.now();
        DateTime expTime = now.offsetNew(DateField.DAY_OF_YEAR, 7);
        Map<String, Object> payload = new HashMap<>();
        // 签发时间
        payload.put(JWTPayload.ISSUED_AT, now);
        // 过期时间
        payload.put(JWTPayload.EXPIRES_AT, expTime);
        // 生效时间
        payload.put(JWTPayload.NOT_BEFORE, now);
        // 内容
        payload.put("id", id);
        payload.put("name", name);
        String token = JWTUtil.createToken(payload, KEY.getBytes());
        log.info("生成JWT token：{}", token);
        return token;
    }

    /**
     * 校验token
     * @param token
     * @return
     */
    public static boolean validate(String token) {
        if (StringUtils.isEmpty(token)){
            return false;
        }
        JWT jwt = JWTUtil.parseToken(token).setKey(KEY.getBytes());
        // validate包含了verify
        boolean validate = jwt.validate(0);
        log.info("JWT token校验结果：{}", validate);
        return validate;
    }

    public static JSONObject getJSONObject(String token) {
        JWT jwt = JWTUtil.parseToken(token).setKey(KEY.getBytes());
        JSONObject payloads = jwt.getPayloads();
        payloads.remove(JWTPayload.ISSUED_AT);
        payloads.remove(JWTPayload.EXPIRES_AT);
        payloads.remove(JWTPayload.NOT_BEFORE);
        log.info("根据token获取原始内容：{}", payloads);
        return payloads;
    }

    public static void main(String[] args) {
        String myToken = createToken("obNbassi_BQ5MerjvffiQvGMdKcw", "会勇");
        System.out.println(myToken);
        boolean validate = validate(myToken);
        System.out.println(validate);

        JSONObject jsonObject = getJSONObject(myToken);
        System.out.println(jsonObject);

    }
}
