package com.ruoyi.custom.service.impl;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.TimeUnit;

import cn.hutool.core.util.RandomUtil;
import cn.hutool.extra.tokenizer.TokenizerUtil;
import cn.hutool.jwt.JWTUtil;
import com.alibaba.fastjson2.JSONObject;
import com.ruoyi.common.constant.Constants;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.redis.RedisCache;
import com.ruoyi.common.utils.DateUtils;
import com.ruoyi.common.utils.jwt.JwtUtil;
import com.ruoyi.custom.domain.IntegralRecord;
import com.ruoyi.custom.domain.MpUser;
import com.ruoyi.custom.mapper.IntegralRecordMapper;
import com.ruoyi.custom.mapper.MpUserMapper;
import com.ruoyi.system.service.ISysConfigService;
import lombok.extern.slf4j.Slf4j;
import me.chanjar.weixin.common.error.WxErrorException;
import me.chanjar.weixin.mp.api.WxMpService;
import me.chanjar.weixin.mp.bean.result.WxMpUser;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import com.ruoyi.custom.mapper.ArticleMapper;
import com.ruoyi.custom.domain.Article;
import com.ruoyi.custom.service.IArticleService;
import org.springframework.transaction.annotation.Transactional;

/**
 * 文章Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-05-14
 */
@Slf4j
@Service
public class ArticleServiceImpl implements IArticleService {
    @Autowired
    private ArticleMapper articleMapper;

    @Autowired
    private IntegralRecordMapper integralRecordMapper;

    @Autowired
    private MpUserMapper mpUserMapper;

    @Autowired
    private WxMpService wxMpService;

    @Autowired
    private ISysConfigService sysConfigService;

    @Value("${wechat.mp.baseUrl}")
    private String baseUrl;

//    @Autowired
//    private RedisCache redisCache;
//
//    private static final String WX_MP_ACCESS_TOKEN = "wx_mp_access_token";
//    private static final int ACCESS_TOKEN_EXPIRE_SECONDS = 3600;

    /**
     * 查询文章
     *
     * @param id 文章主键
     * @return 文章
     */
    @Override
    public Article selectArticleById(Long id) {
        return articleMapper.selectArticleById(id);
    }

    /**
     * 查询文章列表
     *
     * @param article 文章
     * @return 文章
     */
    @Override
    public List<Article> selectArticleList(Article article) {
        List<Article> articleList = articleMapper.selectArticleList(article);
        articleList.forEach(item -> {
            item.setUrl(baseUrl + "prod-api/" + item.getUrl());
        });
        return articleList;
    }

    /**
     * 新增文章
     *
     * @param article 文章
     * @return 结果
     */
    @Override
    public int insertArticle(Article article) {
        article.setCreateTime(DateUtils.getNowDate());
        String str = RandomUtil.randomStringUpper(32);
        article.setUrl("web/article/" + str);
        return articleMapper.insertArticle(article);
    }

    /**
     * 修改文章
     *
     * @param article 文章
     * @return 结果
     */
    @Override
    public int updateArticle(Article article) {
        article.setUpdateTime(DateUtils.getNowDate());
        return articleMapper.updateArticle(article);
    }

    /**
     * 批量删除文章
     *
     * @param ids 需要删除的文章主键
     * @return 结果
     */
    @Override
    public int deleteArticleByIds(Long[] ids) {
        return articleMapper.deleteArticleByIds(ids);
    }

    /**
     * 删除文章信息
     *
     * @param id 文章主键
     * @return 结果
     */
    @Override
    public int deleteArticleById(Long id) {
        return articleMapper.deleteArticleById(id);
    }

    @Override
    @Transactional
    public AjaxResult readArticle(String token, String articleId) {
        // 验证token是否有效
        if (!JwtUtil.validate(token)) {
            return AjaxResult.error("token失效,请重新进入！");
        }

        String openid = JwtUtil.getJSONObject(token).getStr("id");

        // 查询用户信息
        MpUser mpUser = mpUserMapper.selectMpUserByOpenid(openid);
        if (mpUser == null) {
            return AjaxResult.error("用户信息获取失败！");
        }
        if (!mpUser.getStatus().equals("0")) {
            return AjaxResult.error("用户状态异常！");
        }

        try {
            // 获取微信用户信息
            WxMpUser wxMpUser = wxMpService.getUserService().userInfo(openid);
            if (!wxMpUser.getSubscribe()) {
                return AjaxResult.error("请先关注公众号！");
            }
        } catch (WxErrorException e) {
            log.error("【公众号错误】公众号配置错误，请稍后再试！{}", e.getMessage());
            return AjaxResult.error("公众号配置错误，请稍后再试！");
        }

        // 查询文章信息
        Article article = articleMapper.selectArticleByUrlLike(articleId);
        if (article == null) {
            return AjaxResult.error("该文章不存在！");
        }
        if (!article.getStatus().equals("0")) {
            return AjaxResult.error("该文章已下架！");
        }

        // 判断是否已经阅读过该文章
        List<IntegralRecord> integralRecords = integralRecordMapper.selectIntegralRecordListByOpenIdAndArticleId(openid,article.getId());
        if (!integralRecords.isEmpty()) {
            return AjaxResult.error("您已经阅读过该文章了！");
        }

        //每日获取积分上限数量
        String dailyMaximumPoints = sysConfigService.selectConfigByKey("custom.article.daily.maximum.points");
        // 查询阅读获得积分数量
        String integralNumber = sysConfigService.selectConfigByKey("custom.article.read.integral");


        //判断是否超出每日获得上限

        //今日已获得积分
        int curNumber = integralRecordMapper.selectDailyMaximumPointsByOpenidAndCurDate(openid);
        if((curNumber + Integer.parseInt(integralNumber)) > Integer.parseInt(dailyMaximumPoints)){
//            return AjaxResult.error("每日获得积分上限!当前已获得积分：" + curNumber + ",此文章积分:" + integralNumber + ",每日上限积分:" + dailyMaximumPoints);
            return AjaxResult.error("每日获得积分上限" + dailyMaximumPoints + "分！");
        }

        // 记录积分变化
        IntegralRecord integralRecord = new IntegralRecord();
        integralRecord.setOpenid(openid);
        integralRecord.setArticleId(article.getId());
        integralRecord.setIntegralNumber(Long.parseLong(integralNumber));
        integralRecord.setType(0L);
        integralRecord.setOperationTime(DateUtils.getNowDate());
        integralRecord.setRemark("阅读文章+" + integralNumber + "积分");
        int i1 = integralRecordMapper.insertIntegralRecord(integralRecord);
        mpUser.setIntegral(mpUser.getIntegral() + Long.parseLong(integralNumber));
        int i2 = mpUserMapper.updateMpUser(mpUser);
        if (i1 < 0 || i2 < 0) {
            throw new RuntimeException("系统繁忙,请稍候再试！");
        }
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("msg","阅读成功，加" + integralNumber + "积分！");
        jsonObject.put("number", integralNumber);
        return AjaxResult.success(jsonObject);
    }

    @Override
    public int selectArticleAllCount() {
        return articleMapper.selectArticleAllCount();
    }

//    private String getAccessToken() {
//        if (redisCache.hasKey(WX_MP_ACCESS_TOKEN)) {
//            return redisCache.getCacheObject(WX_MP_ACCESS_TOKEN);
//        }
//        try {
//            String accessToken = wxMpService.getAccessToken();
//            redisCache.setCacheObject(WX_MP_ACCESS_TOKEN, accessToken, ACCESS_TOKEN_EXPIRE_SECONDS, TimeUnit.SECONDS);
//            return accessToken;
//        } catch (WxErrorException e) {
//            // 这里可以记录日志，然后抛出自定义异常
//            throw new RuntimeException("Failed to retrieve access token", e);
//        }
//    }
}
