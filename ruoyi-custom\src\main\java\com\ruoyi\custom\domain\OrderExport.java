package com.ruoyi.custom.domain;

import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;

import java.util.Date;

/**
 * 订单导出对象
 */
public class OrderExport extends BaseEntity {
    private static final long serialVersionUID = 1L;

    /** 昵称 */
    @Excel(name = "昵称", width = 20)
    private String nickname;

    /** 商品名称 */
    @Excel(name = "商品名称", width = 20)
    private String title;

    /** 兑换码 */
    @Excel(name = "兑换码", width = 20)
    private String redemptionCode;

    /** 姓名 */
    @Excel(name = "姓名")
    private String name;

    /** 手机号 */
    @Excel(name = "手机号")
    private String phone;

    /** 兑奖日期 */
    @Excel(name = "兑奖日期", dateFormat = "yyyy-MM-dd")
    private Date rewardDate;

    public String getNickname() {
        return nickname;
    }

    public void setNickname(String nickname) {
        this.nickname = nickname;
    }

    public String getTitle() {
        return title;
    }

    public void setTitle(String title) {
        this.title = title;
    }

    public String getRedemptionCode() {
        return redemptionCode;
    }

    public void setRedemptionCode(String redemptionCode) {
        this.redemptionCode = redemptionCode;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getPhone() {
        return phone;
    }

    public void setPhone(String phone) {
        this.phone = phone;
    }

    public Date getRewardDate() {
        return rewardDate;
    }

    public void setRewardDate(Date rewardDate) {
        this.rewardDate = rewardDate;
    }
} 