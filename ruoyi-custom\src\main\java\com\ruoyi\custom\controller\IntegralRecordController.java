package com.ruoyi.custom.controller;

import java.util.List;
import javax.servlet.http.HttpServletResponse;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.custom.domain.IntegralRecord;
import com.ruoyi.custom.service.IIntegralRecordService;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.common.core.page.TableDataInfo;

/**
 * 积分记录Controller
 * 
 * <AUTHOR>
 * @date 2024-05-15
 */
@RestController
@RequestMapping("/custom/integralRecord")
public class IntegralRecordController extends BaseController
{
    @Autowired
    private IIntegralRecordService integralRecordService;

    /**
     * 查询积分记录列表
     */
    @PreAuthorize("@ss.hasPermi('custom:integralRecord:list')")
    @GetMapping("/list")
    public TableDataInfo list(IntegralRecord integralRecord)
    {
        List<IntegralRecord> list = integralRecordService.selectIntegralRecordList(integralRecord);
        return getDataTable(list);
    }

    /**
     * 导出积分记录列表
     */
    @PreAuthorize("@ss.hasPermi('custom:integralRecord:export')")
    @Log(title = "积分记录", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, IntegralRecord integralRecord)
    {
        List<IntegralRecord> list = integralRecordService.selectIntegralRecordList2(integralRecord);
        ExcelUtil<IntegralRecord> util = new ExcelUtil<IntegralRecord>(IntegralRecord.class);
        util.exportExcel(response, list, "积分记录数据");
    }

    /**
     * 获取积分记录详细信息
     */
    @PreAuthorize("@ss.hasPermi('custom:integralRecord:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return success(integralRecordService.selectIntegralRecordById(id));
    }

    /**
     * 新增积分记录
     */
    @PreAuthorize("@ss.hasPermi('custom:integralRecord:add')")
    @Log(title = "积分记录", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody IntegralRecord integralRecord)
    {
        return toAjax(integralRecordService.insertIntegralRecord(integralRecord));
    }

    /**
     * 修改积分记录
     */
    @PreAuthorize("@ss.hasPermi('custom:integralRecord:edit')")
    @Log(title = "积分记录", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody IntegralRecord integralRecord)
    {
        return toAjax(integralRecordService.updateIntegralRecord(integralRecord));
    }

    /**
     * 删除积分记录
     */
    @PreAuthorize("@ss.hasPermi('custom:integralRecord:remove')")
    @Log(title = "积分记录", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        return toAjax(integralRecordService.deleteIntegralRecordByIds(ids));
    }
}
