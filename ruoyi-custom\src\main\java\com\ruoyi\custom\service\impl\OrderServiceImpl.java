package com.ruoyi.custom.service.impl;

import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

import cn.hutool.core.date.DateField;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUnit;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.RandomUtil;
import com.alibaba.fastjson2.JSONObject;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.utils.DateUtils;
import com.ruoyi.common.utils.jwt.JwtUtil;
import com.ruoyi.custom.domain.*;
import com.ruoyi.custom.mapper.GoodsMapper;
import com.ruoyi.custom.mapper.IntegralRecordMapper;
import com.ruoyi.custom.mapper.MpUserMapper;
import com.ruoyi.custom.service.IExchangeRecordService;
import com.ruoyi.custom.service.WorkdayCalculatorService;
import com.ruoyi.system.service.ISysConfigService;
import lombok.extern.slf4j.Slf4j;
import me.chanjar.weixin.common.error.WxErrorException;
import me.chanjar.weixin.mp.api.WxMpService;
import me.chanjar.weixin.mp.bean.result.WxMpUser;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;
import com.ruoyi.custom.mapper.OrderMapper;
import com.ruoyi.custom.service.IOrderService;
import org.springframework.transaction.annotation.Transactional;

/**
 * 订单Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-05-16
 */
@Service
@Slf4j
public class OrderServiceImpl implements IOrderService {
    @Autowired
    private OrderMapper orderMapper;

    @Autowired
    private GoodsMapper goodsMapper;

    @Autowired
    private MpUserMapper mpUserMapper;

    @Autowired
    private WxMpService wxMpService;

    @Autowired
    private IntegralRecordMapper integralRecordMapper;

    @Autowired
    private ISysConfigService sysConfigService;

    @Autowired
    private IExchangeRecordService exchangeRecordService;

    @Autowired
    private WorkdayCalculatorService workdayCalculator;

    @Autowired
    private StringRedisTemplate redisTemplate;

    /**
     * 查询订单
     *
     * @param id 订单主键
     * @return 订单
     */
    @Override
    public Order selectOrderById(Long id) {
        return orderMapper.selectOrderById(id);
    }

    /**
     * 查询订单列表
     *
     * @param order 订单
     * @return 订单
     */
    @Override
    public List<Order> selectOrderList(Order order) {

        if (order.getNickname() != null) {
            List<MpUser> mpUserList = mpUserMapper.selectMpUserByNicknameLike(order.getNickname());
            if (!mpUserList.isEmpty()){
                List<String> openIdList = mpUserList.stream().map(MpUser::getOpenid).collect(Collectors.toList());
                order.setOpenidList(openIdList);
            }else {
                order.setOpenidList(Collections.singletonList("-1"));
            }
        }

        List<Order> orderList = orderMapper.selectOrderList(order);
        orderList.forEach(item -> {
            Goods goods = goodsMapper.selectGoodsById(item.getGoodsId());
            if (goods != null) {
                item.setGoodsName(goods.getTitle());
            } else {
                item.setGoodsName("商品已被删除");
            }
            MpUser mpUser = mpUserMapper.selectMpUserByOpenid(item.getOpenid());
            if (mpUser != null) {
                item.setNickname(mpUser.getNickname());
            }
        });
        return orderList;
    }

    /**
     * 新增订单
     *
     * @param order 订单
     * @return 结果
     */
    @Override
    public int insertOrder(Order order) {
        order.setCreateTime(DateUtils.getNowDate());
        return orderMapper.insertOrder(order);
    }

    /**
     * 修改订单
     *
     * @param order 订单
     * @return 结果
     */
    @Override
    public int updateOrder(Order order) {
        order.setUpdateTime(DateUtils.getNowDate());
        return orderMapper.updateOrder(order);
    }

    /**
     * 批量删除订单
     *
     * @param ids 需要删除的订单主键
     * @return 结果
     */
    @Override
    public int deleteOrderByIds(Long[] ids) {
        return orderMapper.deleteOrderByIds(ids);
    }

    /**
     * 删除订单信息
     *
     * @param id 订单主键
     * @return 结果
     */
    @Override
    public int deleteOrderById(Long id) {
        return orderMapper.deleteOrderById(id);
    }

    @Override
    public AjaxResult orderStatus(Long oId) {
        Order order = orderMapper.selectOrderById(oId);
        String dateLimit = sysConfigService.selectConfigByKey("custom.goods.delivery.date.limit");

        String weekday = workdayCalculator.getWeekdayLimit(order.getCreateTime(), dateLimit);
        if (weekday.equals("error")){
            return AjaxResult.error("节假日信息查询接口请求次数上限,请稍候再试!");
        }
        String date = weekday + " " + DateUtil.format(order.getCreateTime(), "HH:mm:ss");
        if (DateUtil.parse(date).isBefore(new Date())) {
            return AjaxResult.warn("兑换时间已过" + dateLimit + "个工作日");
        }
        order.setStatus("1");
        order.setExchangeTime(DateUtils.getNowDate());
        if (orderMapper.updateOrder(order) > 0) {
            return AjaxResult.success();
        }
        return AjaxResult.error("系统繁忙,请稍候再试!");
    }

    @Override
    @Transactional
    public synchronized AjaxResult exchangeGoods(String token, Long goodsId) {
        String openid = null;
        String lockKey = null;
        
        long exchangeStartTime = System.currentTimeMillis();
        try {
            log.info("【积分兑换】开始处理兑换请求 - token:{}, goodsId:{}, 开始时间:{}", token, goodsId, exchangeStartTime);
            // 参数校验
            if (token == null || goodsId == null) {
                log.warn("【积分兑换】参数校验失败 - token:{}, goodsId:{}", token, goodsId);
                return AjaxResult.error("参数错误");
            }
            // 验证token
            if (!JwtUtil.validate(token)) {
                log.warn("【积分兑换】token验证失败 - token:{}", token);
                return AjaxResult.error("token失效，请重新进入！");
            }

            // 获取用户信息
            openid = JwtUtil.getJSONObject(token).getStr("id");

            // 使用商品级别的分布式锁，确保同一商品同时只能有一个兑换操作
            lockKey = "exchange_goods_lock:" + goodsId;
            boolean locked = Boolean.TRUE.equals(redisTemplate.opsForValue().setIfAbsent(lockKey, openid, 30, TimeUnit.SECONDS));
            if (!locked) {
                log.warn("【积分兑换】获取商品锁失败，商品正在被其他用户兑换 - openid:{}, goodsId:{}", openid, goodsId);
                return AjaxResult.error("商品兑换繁忙，请稍后再试！");
            }
            log.info("【积分兑换】成功获取商品锁 - openid:{}, goodsId:{}, lockKey:{}", openid, goodsId, lockKey);

            // 获取用户信息
            log.info("【积分兑换】获取用户信息 - openid:{}", openid);
            MpUser mpUser = mpUserMapper.selectMpUserByOpenid(openid);
            if (mpUser == null) {
                log.error("【积分兑换】用户信息获取失败 - openid:{}", openid);
                return AjaxResult.error("用户信息获取失败！");
            }
            if (!mpUser.getStatus().equals("0")) {
                log.error("【积分兑换】用户状态异常 - openid:{}, status:{}", openid, mpUser.getStatus());
                return AjaxResult.error("用户状态异常！");
            }
            log.info("【积分兑换】用户信息验证通过 - openid:{}, userIntegral:{}, userStatus:{}",
                openid, mpUser.getIntegral(), mpUser.getStatus());

            try {
                // 验证用户是否关注公众号
                log.info("【积分兑换】验证用户是否关注公众号 - openid:{}", openid);
                WxMpUser wxMpUser = wxMpService.getUserService().userInfo(openid);
                if (!wxMpUser.getSubscribe()) {
                    log.warn("【积分兑换】用户未关注公众号 - openid:{}", openid);
                    return AjaxResult.error("请先关注公众号！");
                }
            } catch (WxErrorException e) {
                log.error("【积分兑换】验证用户关注状态异常 - openid:{}, error:{}", openid, e.getMessage());
                return AjaxResult.error("系统繁忙，请稍候再试！");
            }

            // 获取商品信息（不加锁，只用于检查商品状态和积分）
            log.info("【积分兑换】获取商品信息 - goodsId:{}", goodsId);
            Goods goods = goodsMapper.selectGoodsById(goodsId);
            if (goods == null) {
                log.error("【积分兑换】商品信息获取失败 - goodsId:{}", goodsId);
                return AjaxResult.error("商品信息获取失败！");
            }
            log.info("【积分兑换】商品信息获取成功 - goodsId:{}, title:{}, stock:{}, integral:{}, status:{}",
                goodsId, goods.getTitle(), goods.getStock(), goods.getIntegral(), goods.getStatus());

            if (!goods.getStatus().equals("0")) {
                log.error("【积分兑换】商品状态异常 - goodsId:{}, status:{}", goodsId, goods.getStatus());
                return AjaxResult.error("商品状态异常！");
            }

            // 基本库存检查（实际库存检查在原子更新时进行）
            if (goods.getStock() <= 0) {
                log.warn("【积分兑换】商品库存不足，提前拦截 - goodsId:{}, stock:{}", goodsId, goods.getStock());
                return AjaxResult.error("商品库存不足！");
            }

            if (mpUser.getIntegral() < goods.getIntegral()) {
                log.warn("【积分兑换】用户积分不足 - openid:{}, userIntegral:{}, needIntegral:{}",
                    openid, mpUser.getIntegral(), goods.getIntegral());
                return AjaxResult.error("积分不足！");
            }
            log.info("【积分兑换】积分验证通过 - openid:{}, userIntegral:{}, needIntegral:{}",
                openid, mpUser.getIntegral(), goods.getIntegral());
            // 每件商品每日限制兑换数量
            String goodsDailyLimit = sysConfigService.selectConfigByKey("custom.goods.daily.limit");
            //查询今日当前商品兑换数量
            int exchangeCount = exchangeRecordService.selectExchangeRecordByUidAndGoodsId(openid, goodsId);
            log.info("【积分兑换】检查每日兑换限制 - openid:{}, goodsId:{}, dailyLimit:{}, currentCount:{}", 
                openid, goodsId, goodsDailyLimit, exchangeCount);
            if (exchangeCount >= Integer.parseInt(goodsDailyLimit)) {
                log.warn("【积分兑换】超出每日兑换限制 - openid:{}, goodsId:{}, dailyLimit:{}, currentCount:{}",
                    openid, goodsId, goodsDailyLimit, exchangeCount);
                return AjaxResult.error("当前商品每日兑换数量已达上限！");
            }

            // 1. 原子性减库存和增加销量（防止超卖的关键操作）
            Long oldStock = goods.getStock();
            Long oldSales = goods.getSalesVolume();
            log.warn("【积分兑换-关键步骤】开始执行原子性库存扣减 - openid:{}, goodsId:{}, 当前库存:{}, 当前销量:{}, 线程:{}",
                openid, goodsId, oldStock, oldSales, Thread.currentThread().getName());

            long startTime = System.currentTimeMillis();
            int stockUpdateResult = goodsMapper.decreaseStockAndIncreaseSales(goodsId);
            long endTime = System.currentTimeMillis();

            if (stockUpdateResult <= 0) {
                log.error("【积分兑换-超卖检测】原子性库存扣减失败，可能发生超卖尝试 - openid:{}, goodsId:{}, 影响行数:{}, 执行耗时:{}ms, 线程:{}",
                    openid, goodsId, stockUpdateResult, (endTime - startTime), Thread.currentThread().getName());
                return AjaxResult.error("商品库存不足！");
            }

            log.warn("【积分兑换-关键步骤】原子性库存扣减成功 - openid:{}, goodsId:{}, 影响行数:{}, 预期新库存:{}, 预期新销量:{}, 执行耗时:{}ms, 线程:{}",
                openid, goodsId, stockUpdateResult, oldStock - 1, oldSales + 1, (endTime - startTime), Thread.currentThread().getName());

            // 验证库存扣减后的状态（用于超卖检测）
            verifyStockAfterDecrease(goodsId, openid, oldStock, oldSales);

            // 2. 扣减用户积分
            Long oldIntegral = mpUser.getIntegral();
            mpUser.setIntegral(mpUser.getIntegral() - goods.getIntegral());
            log.warn("【积分兑换-关键步骤】开始扣减用户积分 - openid:{}, goodsId:{}, 原积分:{}, 扣减积分:{}, 新积分:{}",
                openid, goodsId, oldIntegral, goods.getIntegral(), mpUser.getIntegral());
            int i2 = mpUserMapper.updateMpUser(mpUser);
            if (i2 <= 0) {
                log.error("【积分兑换-异常】用户积分扣减失败 - openid:{}, goodsId:{}", openid, goodsId);
                throw new RuntimeException("更新用户积分失败！");
            }
            log.info("【积分兑换-关键步骤】用户积分扣减成功 - openid:{}, goodsId:{}, 影响行数:{}", openid, goodsId, i2);

            // 3. 创建订单
            Order order = new Order();
            order.setOpenid(openid);
            order.setGoodsId(goods.getId());
            order.setStatus("0");
            order.setIntegralNumber(goods.getIntegral());
            String snowflakeNextIdStr = IdUtil.getSnowflakeNextIdStr();
            order.setRedemptionCode(snowflakeNextIdStr);
            order.setCreateTime(DateUtils.getNowDate());
            log.warn("【积分兑换-关键步骤】开始创建订单 - openid:{}, goodsId:{}, 兑换码:{}, 积分:{}",
                openid, goodsId, snowflakeNextIdStr, goods.getIntegral());
            int i3 = orderMapper.insertOrder(order);
            if (i3 <= 0) {
                log.error("【积分兑换-异常】订单创建失败 - openid:{}, goodsId:{}, 兑换码:{}", openid, goodsId, snowflakeNextIdStr);
                throw new RuntimeException("创建订单失败！");
            }
            log.info("【积分兑换-关键步骤】订单创建成功 - openid:{}, goodsId:{}, 兑换码:{}, 影响行数:{}",
                openid, goodsId, snowflakeNextIdStr, i3);

            // 4. 记录积分变动
            IntegralRecord integralRecord = new IntegralRecord();
            integralRecord.setOpenid(openid);
            integralRecord.setIntegralNumber(goods.getIntegral());
            integralRecord.setType(1L);
            integralRecord.setRemark("兑换商品【" + goods.getTitle() + "】，消耗" + goods.getIntegral() + "积分");
            integralRecord.setOperationTime(DateUtils.getNowDate());
            log.warn("【积分兑换】记录积分变动 - openid:{}, integralNumber:{}, remark:{}", openid, goods.getIntegral(), integralRecord.getRemark());
            int i4 = integralRecordMapper.insertIntegralRecord(integralRecord);
            if (i4 <= 0) {
                throw new RuntimeException("记录积分变动失败！");
            }

            // 5. 记录每日兑换统计
            ExchangeRecord exchangeRecord = new ExchangeRecord();
            exchangeRecord.setuId(openid);
            exchangeRecord.setgId(goods.getId());
            exchangeRecord.setExchangeDate(DateUtils.getNowDate());
            exchangeRecord.setStatus(0L);
            log.warn("【积分兑换】记录每日兑换统计 - openid:{}, goodsId:{}, exchangeDate:{}", openid, goodsId, exchangeRecord.getExchangeDate());
            int i5 = exchangeRecordService.insertExchangeRecord(exchangeRecord);
            if (i5 <= 0) {
                throw new RuntimeException("记录每日兑换统计失败！");
            }

            long exchangeEndTime = System.currentTimeMillis();
            log.warn("【积分兑换-成功】兑换流程全部完成 - openid:{}, 用户:{}, 商品:{}, 积分:{}, 兑换码:{}, 总耗时:{}ms",
                openid, mpUser.getNickname(), goods.getTitle(), goods.getIntegral(), snowflakeNextIdStr,
                (exchangeEndTime - exchangeStartTime));
            
            // 获取配送地址
            JSONObject result = new JSONObject();
            // 构造返回结果
            result.put("datetime", DateUtil.format(order.getCreateTime(), "yyyy-MM-dd HH:mm:ss"));
            result.put("goodsName", goods.getTitle());
            result.put("integralNumber", goods.getIntegral());
            result.put("redemptionCode", snowflakeNextIdStr);

            //兑换截止时间
            String dateLimit = sysConfigService.selectConfigByKey("custom.goods.delivery.date.limit");
            String address = sysConfigService.selectConfigByKey("custom.goods.delivery.address");

            String weekday = workdayCalculator.getWeekdayLimit(order.getCreateTime(), dateLimit);
            if (weekday.equals("error")){
                result.put("address", address);
            }else {
                String date = weekday + " " + DateUtil.format(order.getCreateTime(), "HH:mm:ss");
                address = address.replace("工作日内领取", "工作日内领取(" + date + ")之前");
                result.put("address", address);
            }

            return AjaxResult.success(result);
        } catch (Exception e) {
            long exchangeEndTime = System.currentTimeMillis();
            log.error("【积分兑换-异常】兑换流程异常终止 - openid:{}, goodsId:{}, 异常信息:{}, 总耗时:{}ms",
                openid, goodsId, e.getMessage(), (exchangeEndTime - exchangeStartTime), e);
            throw new RuntimeException("系统繁忙，请稍后重试");
        } finally {
            // 释放分布式锁
            if (lockKey != null) {
                boolean deleted = Boolean.TRUE.equals(redisTemplate.delete(lockKey));
                log.info("【积分兑换】释放商品锁 - openid:{}, goodsId:{}, lockKey:{}, 释放结果:{}",
                    openid, goodsId, lockKey, deleted);
            }
        }
    }

    @Override
    public AjaxResult orderList(String token) {
        // 验证token是否有效
        if (!JwtUtil.validate(token)) {
            return AjaxResult.error("token失效,请重新进入！");
        }
        //兑换截止时间
        String dateLimit = sysConfigService.selectConfigByKey("custom.goods.delivery.date.limit");

        String openid = JwtUtil.getJSONObject(token).getStr("id");
        List<Order> orders = orderMapper.selectOrderListByOpenid(openid);
        for (Order order : orders) {
            // 获取配送地址
            String address = sysConfigService.selectConfigByKey("custom.goods.delivery.address");

            String weekday = workdayCalculator.getWeekdayLimit(order.getCreateTime(), dateLimit);
            if (weekday.equals("error")){
                order.setAddress(address);
            }else {
                String date = weekday + " " + DateUtil.format(order.getCreateTime(), "HH:mm:ss");
                address = address.replace("工作日内领取", "工作日内领取(" + date + "之前)");
                order.setAddress(address);
            }
            Goods goods = goodsMapper.selectGoodsById(order.getGoodsId());
            if (goods != null){
                order.setGoodsName(goods.getTitle());
            }
        }
        return AjaxResult.success(orders);
    }

    @Override
    public int selectOrderAllCount() {
        return orderMapper.selectOrderAllCount();
    }

    @Override
    public List<JSONObject> numberOfExchanges(List<String> dateRangeList) {
        return orderMapper.numberOfExchanges(dateRangeList);
    }

    @Override
    public List<JSONObject> numberOfSales(List<String> dateRangeList) {
        return orderMapper.numberOfSales(dateRangeList);
    }

    @Override
    public List<JSONObject> selectOrderByIds(List<String> ids) {
        return orderMapper.selectOrderByIds(ids);
    }

    /**
     * 验证库存扣减后的状态，用于超卖检测
     */
    private void verifyStockAfterDecrease(Long goodsId, String openid, Long oldStock, Long oldSales) {
        try {
            // 重新查询商品信息，验证库存状态
            Goods currentGoods = goodsMapper.selectGoodsById(goodsId);
            if (currentGoods != null) {
                Long currentStock = currentGoods.getStock();
                Long currentSales = currentGoods.getSalesVolume();

                // 检查库存是否符合预期
                Long expectedStock = oldStock - 1;
                Long expectedSales = oldSales + 1;

                if (!currentStock.equals(expectedStock) || !currentSales.equals(expectedSales)) {
                    log.error("【积分兑换-超卖警告】库存状态异常 - openid:{}, goodsId:{}, 原库存:{}, 当前库存:{}, 预期库存:{}, 原销量:{}, 当前销量:{}, 预期销量:{}",
                        openid, goodsId, oldStock, currentStock, expectedStock, oldSales, currentSales, expectedSales);
                }

                // 检查是否出现负库存
                if (currentStock < 0) {
                    log.error("【积分兑换-超卖检测】发现负库存！！！ - openid:{}, goodsId:{}, 当前库存:{}",
                        openid, goodsId, currentStock);
                }

                log.info("【积分兑换-库存验证】库存状态正常 - openid:{}, goodsId:{}, 当前库存:{}, 当前销量:{}",
                    openid, goodsId, currentStock, currentSales);
            }
        } catch (Exception e) {
            log.error("【积分兑换-库存验证】验证库存状态时发生异常 - openid:{}, goodsId:{}", openid, goodsId, e);
        }
    }

    // 添加工具方法
    private long getExpireSeconds() {
        DateTime endOfDay = DateUtil.endOfDay(new Date());
        return DateUtil.between(new Date(), endOfDay, DateUnit.SECOND);
    }
}
