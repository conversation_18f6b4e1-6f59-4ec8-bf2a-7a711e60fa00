package com.ruoyi.custom.service;

import com.alibaba.fastjson2.JSONObject;
import com.ruoyi.common.core.redis.RedisCache;
import com.ruoyi.custom.util.HttpUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.apache.http.HttpResponse;
import org.apache.http.util.EntityUtils;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.TimeUnit;

/**
 * @Author: y
 * @CreateDate: 2025/1/2 002 15:55
 * @Version: 1.0
 * @Description:
 */
@Service
@Slf4j
public class WorkdayCalculatorService {

    @Autowired
    private RedisCache redisCache;
    
    @Value("${holiday.api.appcode:595ed8e59f1e413ea5b4d3c2f1fe541e}")
    private String appcode;
    
    private static final String HOST = "https://jmhlysjjr.market.alicloudapi.com";
    private static final String PATH = "/holiday/detail";
    private static final int CACHE_DAYS = 360;
    private static final String DATE_FORMAT = "yyyy-MM-dd";

    public String getWeekdayLimit(Date date, String dateLimit) {
        try {
            int workdaysToAdd = Integer.parseInt(dateLimit);
            SimpleDateFormat sdf = new SimpleDateFormat(DATE_FORMAT);
            String currentDateStr = sdf.format(date);

            String cacheKey = String.format("workday_limit_%s_%d", currentDateStr, workdaysToAdd);
            if (redisCache.hasKey(cacheKey)) {
                String cachedDate = redisCache.getCacheObject(cacheKey);
                log.info("从缓存中获取日期：{}", cachedDate);
                return cachedDate;
            }

            String targetDate = calculateTargetDate(currentDateStr, workdaysToAdd);
            if (targetDate != null) {
                redisCache.setCacheObject(cacheKey, targetDate, CACHE_DAYS, TimeUnit.DAYS);
                log.info("计算得到目标日期：{}", targetDate);
                return targetDate;
            }
            
            return "error";
        } catch (NumberFormatException e) {
            log.error("日期限制参数格式错误: {}", dateLimit, e);
            return "error";
        }
    }

    private String calculateTargetDate(String startDate, int workdaysToAdd) {
        int daysAdded = 0;
        String currentDate = startDate;

        while (daysAdded < workdaysToAdd) {
            currentDate = addOneDay(currentDate);
            if (currentDate == null) return null;
            
            if (isWorkday(currentDate)) {
                daysAdded++;
            }
        }
        
        return currentDate;
    }

    private String addOneDay(String date) {
        try {
            SimpleDateFormat sdf = new SimpleDateFormat(DATE_FORMAT);
            Date currentDate = sdf.parse(date);
            return sdf.format(new Date(currentDate.getTime() + TimeUnit.DAYS.toMillis(1)));
        } catch (Exception e) {
            log.error("日期转换错误: {}", date, e);
            return null;
        }
    }

    private boolean isWorkday(String date) {
        // 检查缓存中是否已有该日期的查询结果
        String cacheKey = "workday_status_" + date;
        Boolean cachedResult = redisCache.getCacheObject(cacheKey);
        if (cachedResult != null) {
            log.info("从缓存中获取工作日状态: {} -> {}", date, cachedResult);
            return cachedResult;
        }

        try {
            Map<String, String> headers = new HashMap<>();
            headers.put("Authorization", "APPCODE " + appcode);
            headers.put("Content-Type", "application/x-www-form-urlencoded; charset=UTF-8");

            Map<String, String> bodys = new HashMap<>();
            bodys.put("date", date.replace("-", ""));
            bodys.put("needDesc", "0");

            HttpResponse response = HttpUtils.doPost(HOST, PATH, "POST", headers, new HashMap<>(), bodys);
            String responseBody = EntityUtils.toString(response.getEntity());
            JSONObject jsonObject = JSONObject.parseObject(responseBody);
            
            boolean isWorkday = false;
            if (jsonObject.getInteger("code") == 200) {
                JSONObject data = jsonObject.getJSONObject("data");
                isWorkday = "1".equals(data.getString("type"));
            } else {
                log.error("API调用失败: {}", jsonObject.getString("msg"));
            }
            
            // 将结果缓存到Redis中，缓存时间设置为1年
            redisCache.setCacheObject(cacheKey, isWorkday, 90, TimeUnit.DAYS);
            log.debug("缓存工作日状态: {} -> {}", date, isWorkday);
            
            return isWorkday;
        } catch (Exception e) {
            log.error("请求过程中发生错误: {}", date, e);
            return false;
        }
    }
}
