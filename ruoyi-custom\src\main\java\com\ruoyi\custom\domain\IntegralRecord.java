package com.ruoyi.custom.domain;

import java.util.Date;
import java.util.List;

import com.fasterxml.jackson.annotation.JsonFormat;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;
import org.apache.poi.ss.usermodel.HorizontalAlignment;
import org.springframework.data.annotation.Transient;

/**
 * 积分记录对象 tbl_integral_record
 * 
 * <AUTHOR>
 * @date 2024-05-15
 */
public class IntegralRecord extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** $column.columnComment */
    private Long id;

    /** $column.columnComment */
    @Excel(name = "用户微信openid",width = 30, align = HorizontalAlignment.LEFT)
    private String openid;

    @Transient
    private List<String> openidList;

    @Transient
    @Excel(name = "微信昵称")
    private String nickname;
    /** $column.columnComment */
    private Long articleId;

    @Transient
    @Excel(name = "推文标题",width = 50, align = HorizontalAlignment.LEFT)
    private String articleTitle;

    /** $column.columnComment */
    @Excel(name = "获得/消耗积分")
    private Long integralNumber;

    /** 默认0获得积分1消耗积分 */
    private Long type;

    /** $column.columnComment */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "操作时间",dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date operationTime;

    public void setId(Long id) 
    {
        this.id = id;
    }

    public Long getId()
    {
        return id;
    }

    public List<String> getOpenidList() {
        return openidList;
    }

    public void setOpenidList(List<String> openidList) {
        this.openidList = openidList;
    }

    public String getNickname() {
        return nickname;
    }

    public void setNickname(String nickname) {
        this.nickname = nickname;
    }

    public void setOpenid(String openid)
    {
        this.openid = openid;
    }

    public String getOpenid() 
    {
        return openid;
    }
    public void setArticleId(Long articleId) 
    {
        this.articleId = articleId;
    }

    public Long getArticleId() 
    {
        return articleId;
    }
    public void setIntegralNumber(Long integralNumber) 
    {
        this.integralNumber = integralNumber;
    }

    public Long getIntegralNumber() 
    {
        return integralNumber;
    }
    public void setType(Long type) 
    {
        this.type = type;
    }

    public Long getType() 
    {
        return type;
    }
    public void setOperationTime(Date operationTime) 
    {
        this.operationTime = operationTime;
    }

    public Date getOperationTime() 
    {
        return operationTime;
    }

    public String getArticleTitle() {
        return articleTitle;
    }

    public void setArticleTitle(String articleTitle) {
        this.articleTitle = articleTitle;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("id", getId())
            .append("openid", getOpenid())
            .append("articleId", getArticleId())
            .append("integralNumber", getIntegralNumber())
            .append("type", getType())
            .append("remark", getRemark())
            .append("operationTime", getOperationTime())
            .append("createTime", getCreateTime())
            .append("updateTime", getUpdateTime())
            .toString();
    }
}
