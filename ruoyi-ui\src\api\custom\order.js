import request from '@/utils/request'

// 查询订单列表
export function listOrder(query) {
  return request({
    url: '/custom/order/list',
    method: 'get',
    params: query
  })
}

// 查询订单详细
export function getOrder(id) {
  return request({
    url: '/custom/order/' + id,
    method: 'get'
  })
}

// 新增订单
export function addOrder(data) {
  return request({
    url: '/custom/order',
    method: 'post',
    data: data
  })
}

// 修改订单
export function updateOrder(data) {
  return request({
    url: '/custom/order',
    method: 'put',
    data: data
  })
}
// 修改订单
export function updateOrderStatus(id) {
  return request({
    url: '/custom/order/orderStatus/' + id,
    method: 'put',
  })
}

// 删除订单
export function delOrder(id) {
  return request({
    url: '/custom/order/' + id,
    method: 'delete'
  })
}
