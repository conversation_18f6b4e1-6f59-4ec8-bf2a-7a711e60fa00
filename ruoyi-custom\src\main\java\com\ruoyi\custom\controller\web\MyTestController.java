package com.ruoyi.custom.controller.web;

import cn.hutool.core.date.DateUtil;
import com.ruoyi.custom.service.WorkdayCalculatorService;
import me.chanjar.weixin.common.error.WxErrorException;
import me.chanjar.weixin.mp.api.WxMpService;
import org.apache.ibatis.annotations.Param;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * @Author: y
 * @CreateDate: 2025/2/17 017 15:36
 * @Version: 1.0
 * @Description:
 */
@RequestMapping("/test")
@RestController
public class MyTestController {

    @Autowired
    private WorkdayCalculatorService workdayCalculatorService;

    @Autowired
    private WxMpService wxMpService;

    @RequestMapping("/hello")
    public String hello(@Param("date")String date) throws WxErrorException {
        return workdayCalculatorService.getWeekdayLimit(DateUtil.parse(date),"30");
    }

    @GetMapping("/testToken/{str}")
    public String testToken(@PathVariable("str") String str) throws WxErrorException {
        if (!str.equals("refresh")){
            return null;
        }
        return wxMpService.getAccessToken();
    }

}
