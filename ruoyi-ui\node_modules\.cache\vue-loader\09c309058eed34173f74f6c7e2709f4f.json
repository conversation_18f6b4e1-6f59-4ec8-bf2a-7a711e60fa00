{"remainingRequest": "D:\\IDEA_PROJECT\\GongHuiReadIntegral\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\IDEA_PROJECT\\GongHuiReadIntegral\\ruoyi-ui\\src\\views\\custom\\article\\index.vue?vue&type=style&index=0&id=99717416&scoped=true&lang=css", "dependencies": [{"path": "D:\\IDEA_PROJECT\\GongHuiReadIntegral\\ruoyi-ui\\src\\views\\custom\\article\\index.vue", "mtime": 1742345580399}, {"path": "D:\\IDEA_PROJECT\\GongHuiReadIntegral\\ruoyi-ui\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\IDEA_PROJECT\\GongHuiReadIntegral\\ruoyi-ui\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 499162500000}, {"path": "D:\\IDEA_PROJECT\\GongHuiReadIntegral\\ruoyi-ui\\node_modules\\postcss-loader\\src\\index.js", "mtime": 499162500000}, {"path": "D:\\IDEA_PROJECT\\GongHuiReadIntegral\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\IDEA_PROJECT\\GongHuiReadIntegral\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:CgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgouZWwtZHJvcGRvd24gewogIHZlcnRpY2FsLWFsaWduOiB0b3A7Cn0KLmVsLWRyb3Bkb3duICsgLmVsLWRyb3Bkb3duIHsKICBtYXJnaW4tbGVmdDogMTBweDsKfQouZWwtaWNvbi1hcnJvdy1kb3duIHsKICBmb250LXNpemU6IDEycHg7Cn0K"}, {"version": 3, "sources": ["index.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAqYA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "index.vue", "sourceRoot": "src/views/custom/article", "sourcesContent": ["<template>\n  <div class=\"app-container\">\n    <el-form :model=\"queryParams\" ref=\"queryForm\" size=\"small\" :inline=\"true\" v-show=\"showSearch\" label-width=\"68px\">\n      <el-form-item label=\"标题\" prop=\"title\">\n        <el-input\n          v-model=\"queryParams.title\"\n          placeholder=\"请输入标题\"\n          clearable\n          @keyup.enter.native=\"handleQuery\"\n        />\n      </el-form-item>\n<!--      <el-form-item label=\"链接\" prop=\"url\">-->\n<!--        <el-input-->\n<!--          v-model=\"queryParams.url\"-->\n<!--          placeholder=\"请输入链接\"-->\n<!--          clearable-->\n<!--          @keyup.enter.native=\"handleQuery\"-->\n<!--        />-->\n<!--      </el-form-item>-->\n      <el-form-item>\n        <el-button type=\"primary\" icon=\"el-icon-search\" size=\"mini\" @click=\"handleQuery\">搜索</el-button>\n        <el-button icon=\"el-icon-refresh\" size=\"mini\" @click=\"resetQuery\">重置</el-button>\n      </el-form-item>\n    </el-form>\n\n    <el-row :gutter=\"10\" class=\"mb8\">\n      <el-col :span=\"1.5\">\n        <el-button\n          type=\"primary\"\n          plain\n          icon=\"el-icon-plus\"\n          size=\"mini\"\n          @click=\"handleAdd\"\n          v-hasPermi=\"['custom:article:add']\"\n        >新增</el-button>\n      </el-col>\n      <el-col :span=\"1.5\">\n        <el-button\n          type=\"success\"\n          plain\n          icon=\"el-icon-edit\"\n          size=\"mini\"\n          :disabled=\"single\"\n          @click=\"handleUpdate\"\n          v-hasPermi=\"['custom:article:edit']\"\n        >修改</el-button>\n      </el-col>\n      <el-col :span=\"1.5\">\n        <el-button\n          type=\"danger\"\n          plain\n          icon=\"el-icon-delete\"\n          size=\"mini\"\n          :disabled=\"multiple\"\n          @click=\"handleDelete\"\n          v-hasPermi=\"['custom:article:remove']\"\n        >删除</el-button>\n      </el-col>\n      <el-col :span=\"1.5\">\n        <el-button\n          type=\"warning\"\n          plain\n          icon=\"el-icon-download\"\n          size=\"mini\"\n          @click=\"handleExport\"\n          v-hasPermi=\"['custom:article:export']\"\n        >导出</el-button>\n      </el-col>\n      <el-col :span=\"1.5\">\n        <el-dropdown @command=\"handleColumnCommand\" trigger=\"click\">\n          <el-button type=\"info\" plain size=\"mini\">\n            列设置<i class=\"el-icon-arrow-down el-icon--right\"></i>\n          </el-button>\n          <el-dropdown-menu slot=\"dropdown\">\n            <el-dropdown-item v-for=\"item in columns\"\n              :key=\"item.prop\"\n              :command=\"item.prop\"\n              >\n              <el-checkbox v-model=\"item.visible\" @click.native.stop>{{item.label}}</el-checkbox>\n            </el-dropdown-item>\n            <el-dropdown-item divided command=\"reset\">\n              <i class=\"el-icon-refresh\"></i> 重置列设置\n            </el-dropdown-item>\n          </el-dropdown-menu>\n        </el-dropdown>\n      </el-col>\n      <right-toolbar :showSearch.sync=\"showSearch\" @queryTable=\"getList\"></right-toolbar>\n    </el-row>\n\n    <el-table border v-loading=\"loading\" :data=\"articleList\" @selection-change=\"handleSelectionChange\">\n      <el-table-column type=\"selection\" width=\"55\" align=\"center\" />\n      <el-table-column label=\"ID\" align=\"center\" prop=\"id\" min-width=\"50\" v-if=\"columns[0].visible\"/>\n      <el-table-column label=\"推文标题\" header-align=\"center\" align=\"left\" prop=\"title\" width=\"400\" :show-overflow-tooltip=\"true\" v-if=\"columns[1].visible\"/>\n      <el-table-column label=\"原文链接\" align=\"center\" prop=\"url\" min-width=\"550\" :show-overflow-tooltip=\"true\" v-if=\"columns[2].visible\">\n        <template slot-scope=\"scope\">\n          <el-tag type=\"info\" style=\"font-family: monospace; color: #606266\">{{scope.row.url}}</el-tag>\n          &nbsp; <el-tooltip class=\"item\" effect=\"dark\" content=\"点击复制原文链接\" :open-delay=\"500\" placement=\"top-start\">\n              <el-button v-clipboard:copy=\"scope.row.url\"\n                     v-clipboard:success=\"onCopySuccess\"\n                     v-clipboard:error=\"onCopyError\"\n                     size=\"mini\" icon=\"el-icon-document-copy\"></el-button>\n            </el-tooltip>\n        </template>\n      </el-table-column>\n      <el-table-column label=\"阅读数量\" align=\"center\" prop=\"readNumber\" min-width=\"80\" v-if=\"columns[3].visible\"/>\n      <el-table-column label=\"备注\" align=\"center\" prop=\"remark\" min-width=\"120\" :show-overflow-tooltip=\"true\" v-if=\"columns[4].visible\"/>\n      <el-table-column label=\"状态\" align=\"center\" prop=\"status\" min-width=\"80\" :show-overflow-tooltip=\"true\" v-if=\"columns[5].visible\">\n        <template slot-scope=\"scope\">\n          <el-tag v-if=\"scope.row.status == 0\" type=\"success\">正常</el-tag>\n          <el-tag v-else-if=\"scope.row.status == 1\" type=\"danger\">停用</el-tag>\n        </template>\n      </el-table-column>\n      <el-table-column label=\"创建时间\" align=\"center\" prop=\"createTime\" width=\"180\" v-if=\"columns[6].visible\">\n        <template slot-scope=\"scope\">\n          <span>{{ parseTime(scope.row.createTime) }}</span>\n        </template>\n      </el-table-column>\n      <el-table-column label=\"操作\" align=\"center\" class-name=\"small-padding fixed-width\" min-width=\"120\" fixed=\"right\" v-if=\"columns[7].visible\">\n        <template slot-scope=\"scope\">\n          <el-button\n            size=\"mini\"\n            type=\"text\"\n            icon=\"el-icon-edit\"\n            @click=\"handleUpdate(scope.row)\"\n            v-hasPermi=\"['custom:article:edit']\"\n          >修改</el-button>\n          <el-button\n            size=\"mini\"\n            type=\"text\"\n            icon=\"el-icon-delete\"\n            @click=\"handleDelete(scope.row)\"\n            v-hasPermi=\"['custom:article:remove']\"\n          >删除</el-button>\n        </template>\n      </el-table-column>\n    </el-table>\n\n    <pagination\n      v-show=\"total>0\"\n      :total=\"total\"\n      :page.sync=\"queryParams.pageNum\"\n      :limit.sync=\"queryParams.pageSize\"\n      @pagination=\"getList\"\n    />\n\n    <!-- 添加或修改推文对话框 -->\n    <el-dialog :title=\"title\" :visible.sync=\"open\" width=\"40%\" append-to-body>\n      <el-form ref=\"form\" :model=\"form\" :rules=\"rules\" label-width=\"80px\">\n        <el-form-item label=\"标题\" prop=\"title\">\n          <el-input v-model=\"form.title\" placeholder=\"请输入标题\" />\n        </el-form-item>\n        <el-form-item v-if=\"this.form.id\" label=\"链接\" prop=\"url\">\n          <el-input v-model=\"form.url\" placeholder=\"请输入链接\" />\n        </el-form-item>\n        <el-form-item label=\"状态\" prop=\"status\">\n            <el-radio-group v-model=\"form.status\">\n              <el-radio\n                v-for=\"dict in dict.type.sys_normal_disable\"\n                :key=\"dict.value\"\n                :label=\"dict.value\"\n              >{{dict.label}}</el-radio>\n            </el-radio-group>\n        </el-form-item>\n        <el-form-item label=\"备注\" prop=\"remark\">\n          <el-input type=\"textarea\" :rows=\"3\" v-model=\"form.remark\" placeholder=\"请输入备注\" />\n        </el-form-item>\n      </el-form>\n      <div slot=\"footer\" class=\"dialog-footer\">\n        <el-button type=\"primary\" @click=\"submitForm\">确 定</el-button>\n        <el-button @click=\"cancel\">取 消</el-button>\n      </div>\n    </el-dialog>\n  </div>\n</template>\n\n<script>\nimport { listArticle, getArticle, delArticle, addArticle, updateArticle } from \"@/api/custom/article\";\n\nexport default {\n  name: \"Article\",\n  dicts: ['sys_normal_disable'],\n  data() {\n    return {\n      // 遮罩层\n      loading: true,\n      // 选中数组\n      ids: [],\n      // 非单个禁用\n      single: true,\n      // 非多个禁用\n      multiple: true,\n      // 显示搜索条件\n      showSearch: true,\n      // 总条数\n      total: 0,\n      // 推文表格数据\n      articleList: [],\n      // 弹出层标题\n      title: \"\",\n      // 是否显示弹出层\n      open: false,\n      // 查询参数\n      queryParams: {\n        pageNum: 1,\n        pageSize: 10,\n        title: null,\n        url: null,\n        status: null,\n      },\n      // 表单参数\n      form: {},\n      // 表单校验\n      rules: {\n        title: [\n          { required: true, message: \"标题不能为空\", trigger: \"blur\" }\n        ],\n        url: [\n          { required: true, message: \"链接不能为空\", trigger: \"blur\" }\n        ],\n        status: [\n          { required: true, message: \"$comment不能为空\", trigger: \"change\" }\n        ],\n      },\n      // 列显示设置\n      columns: [\n        { label: 'ID', prop: 'id', visible: true },\n        { label: '推文标题', prop: 'title', visible: true },\n        { label: '原文链接', prop: 'url', visible: true },\n        { label: '阅读数量', prop: 'readNumber', visible: true },\n        { label: '备注', prop: 'remark', visible: true },\n        { label: '状态', prop: 'status', visible: true },\n        { label: '创建时间', prop: 'createTime', visible: true },\n        { label: '操作', prop: 'operation', visible: true }\n      ],\n    };\n  },\n  watch: {\n    // 监听columns数组中每个对象的visible属性变化\n    columns: {\n      handler(newVal) {\n        localStorage.setItem('article-table-columns', JSON.stringify(newVal));\n      },\n      deep: true // 深度监听\n    }\n  },\n  created() {\n    // 从localStorage恢复列显示状态\n    const savedColumns = localStorage.getItem('article-table-columns');\n    if (savedColumns) {\n      try {\n        const parsedColumns = JSON.parse(savedColumns);\n        this.columns = this.columns.map((col, index) => ({\n          ...col,\n          visible: parsedColumns[index]?.visible ?? true\n        }));\n      } catch (e) {\n        console.error('Failed to parse saved columns:', e);\n      }\n    }\n    this.getList();\n  },\n  methods: {\n    onCopyError(){\n      this.$message.error(\"复制失败\");\n    },\n    onCopySuccess(){\n      this.$message.success(\"复制成功\");\n    },\n\n    /** 查询推文列表 */\n    getList() {\n      this.loading = true;\n      listArticle(this.queryParams).then(response => {\n        this.articleList = response.rows;\n        this.total = response.total;\n        this.loading = false;\n      });\n    },\n    // 取消按钮\n    cancel() {\n      this.open = false;\n      this.reset();\n    },\n    // 表单重置\n    reset() {\n      this.form = {\n        id: null,\n        title: null,\n        url: null,\n        remark: null,\n        status: null,\n        createTime: null,\n        updateTime: null\n      };\n      this.resetForm(\"form\");\n    },\n    /** 搜索按钮操作 */\n    handleQuery() {\n      this.queryParams.pageNum = 1;\n      this.getList();\n    },\n    /** 重置按钮操作 */\n    resetQuery() {\n      this.resetForm(\"queryForm\");\n      this.handleQuery();\n    },\n    // 多选框选中数据\n    handleSelectionChange(selection) {\n      this.ids = selection.map(item => item.id)\n      this.single = selection.length!==1\n      this.multiple = !selection.length\n    },\n    /** 新增按钮操作 */\n    handleAdd() {\n      this.reset();\n      this.form.status = \"0\";\n      this.open = true;\n      this.title = \"添加推文\";\n    },\n    /** 修改按钮操作 */\n    handleUpdate(row) {\n      this.reset();\n      const id = row.id || this.ids\n      getArticle(id).then(response => {\n        this.form = response.data;\n        this.open = true;\n        this.title = \"修改推文\";\n      });\n    },\n    /** 提交按钮 */\n    submitForm() {\n      this.$refs[\"form\"].validate(valid => {\n        if (valid) {\n          if (this.form.id != null) {\n            updateArticle(this.form).then(response => {\n              this.$modal.msgSuccess(\"修改成功\");\n              this.open = false;\n              this.getList();\n            });\n          } else {\n            addArticle(this.form).then(response => {\n              this.$modal.msgSuccess(\"新增成功\");\n              this.open = false;\n              this.getList();\n            });\n          }\n        }\n      });\n    },\n    /** 删除按钮操作 */\n    handleDelete(row) {\n      const ids = row.id || this.ids;\n      this.$modal.confirm('是否确认删除推文ID为\"' + ids + '\"的数据项？').then(function() {\n        return delArticle(ids);\n      }).then(() => {\n        this.getList();\n        this.$modal.msgSuccess(\"删除成功\");\n      }).catch(() => {});\n    },\n    /** 导出按钮操作 */\n    handleExport() {\n      this.$modal.confirm('是否确认导出所有推文数据？').then(() => {\n        this.download('custom/article/export', {\n          ...this.queryParams\n        }, `article_${new Date().getTime()}.xlsx`)\n      }).catch(() => {});\n    },\n    // 处理列显示/隐藏\n    handleColumnCommand(command) {\n      if (command === 'reset') {\n        this.resetColumns();\n        return;\n      }\n      const column = this.columns.find(item => item.prop === command);\n      if (column) {\n        column.visible = !column.visible;\n      }\n    },\n    // 重置列设置\n    resetColumns() {\n      this.columns.forEach(col => col.visible = true);\n      localStorage.removeItem('article-table-columns');\n      this.$message.success('列设置已重置');\n    },\n  }\n};\n</script>\n\n<style scoped>\n.el-dropdown {\n  vertical-align: top;\n}\n.el-dropdown + .el-dropdown {\n  margin-left: 10px;\n}\n.el-icon-arrow-down {\n  font-size: 12px;\n}\n</style>\n"]}]}